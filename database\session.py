from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager

try:
    from config import DATABASE_URL
except ImportError:
    from config.advanced_config import get_database_url
    DATABASE_URL = get_database_url()
from .base import Base # Pour créer les tables

print(f"INFO: Création de l'engine SQLAlchemy pour {DATABASE_URL}")
engine = create_engine(DATABASE_URL, echo=False) # Mettre echo=True pour voir les requêtes SQL

# Utiliser scoped_session est une bonne pratique pour gérer les sessions par thread
SessionFactory = sessionmaker(bind=engine)
Session = scoped_session(SessionFactory)
print("INFO: Session factory et scoped_session créés.")

def init_db():
    """Crée toutes les tables définies dans les modèles."""
    print("INFO: Tentative de création des tables de la base de données...")
    try:
        Base.metadata.create_all(bind=engine)
        print("INFO: Tables créées (ou existent déjà).")
    except Exception as e:
        print(f"ERREUR CRITIQUE: Impossible de créer les tables : {e}")
        # Gérer l'erreur de manière appropriée

@contextmanager
def session_scope():
    """Fournit une portée transactionnelle autour d'une série d'opérations."""
    session = Session()
    try:
        yield session
        session.commit()
        print("DEBUG: Session commitée.")
    except Exception as e:
        session.rollback()
        print(f"ERREUR: Rollback de la session suite à une exception : {e}")
        raise # Renvoyer l'exception pour la gérer plus haut
    finally:
        Session.remove() # Important pour scoped_session
        print("DEBUG: Session fermée.")

print("INFO: Fonctions de session définies.")