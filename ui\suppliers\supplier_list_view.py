"""
Vue de liste des fournisseurs pour l'application GSCOM.
"""
from PyQt5.QtWidgets import (
    QHBoxLayout,
    QVBoxLayout,
    QWidget,
    QDialog,
    QMessageBox,
    QLabel,
    QFrame
)
from PyQt5.QtCore import Qt, pyqtSlot

from qfluentwidgets import (
    FluentIcon, InfoBar, InfoBarPosition, IconWidget
)

from ..components import BaseTable
from .supplier_form import SupplierForm
from utils.theme_manager import theme_manager
from ui.styles.clean_style import get_clean_style


class SupplierListView(QWidget):
    """Vue de liste des fournisseurs."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        self.loadSampleData()

    def setupUI(self):
        """Configure l'interface utilisateur."""
        # Connecter le signal de changement de thème
        theme_manager.themeChanged.connect(self.onThemeChanged)

        # Appliquer le style initial
        self.updateStyle()

        # Désactiver la sélection de texte sur tous les QLabel
        def disable_label_selection(widget):
            from PyQt5.QtWidgets import QLabel
            from PyQt5.QtCore import Qt
            for child in widget.findChildren(QLabel):
                child.setTextInteractionFlags(Qt.NoTextInteraction)
        disable_label_selection(self)

        layout = QVBoxLayout(self)

        # Bandeau supérieur moderne
        topBar = QHBoxLayout()
        titleIcon = IconWidget(FluentIcon.PEOPLE, self)
        titleIcon.setFixedSize(32, 32)
        titleLabel = QLabel("Liste des fournisseurs", self)
        titleLabel.setStyleSheet("font-size: 22px; font-weight: bold; margin-left: 10px;")
        topBar.addWidget(titleIcon)
        topBar.addWidget(titleLabel)
        topBar.addStretch()
        layout.addLayout(topBar)

        # Ombre sous le bandeau supérieur
        shadowFrame = QFrame(self)
        shadowFrame.setFixedHeight(2)
        shadowFrame.setStyleSheet("background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #38BDF8, stop:1 #0ea5e9); border-radius: 1px;")
        layout.addWidget(shadowFrame)

        # Créer le tableau des fournisseurs
        self.supplierTable = BaseTable(
            title="Liste des Fournisseurs",
            headers=["Code", "Nom", "Adresse", "Téléphone", "Email", "Solde"],
            parent=self
        )

        # Connecter les signaux
        self.supplierTable.addButton.clicked.connect(self.openSupplierForm)
        self.supplierTable.rowDoubleClicked.connect(self.editSupplier)
        self.supplierTable.rowDeleted.connect(self.deleteSupplier)

        layout.addWidget(self.supplierTable)

    def loadSampleData(self):
        """Charge des données d'exemple."""
        sample_data = [
            ["FOUR001", "TechnoPlus SARL", "Zone Industrielle, Alger", "021-123-456", "<EMAIL>", "15 000,00 DA"],
            ["FOUR002", "ElectroMag", "Rue des Frères Bouadou, Oran", "041-789-012", "<EMAIL>", "8 500,00 DA"],
            ["FOUR003", "InfoSys Solutions", "Cité El Badr, Constantine", "031-456-789", "<EMAIL>", "22 750,00 DA"],
            ["FOUR004", "Digital World", "Boulevard Mohamed V, Annaba", "038-321-654", "<EMAIL>", "12 300,00 DA"],
            ["FOUR005", "Micro Center", "Avenue de l'Indépendance, Sétif", "036-987-321", "<EMAIL>", "6 800,00 DA"]
        ]

        # Ajouter les données au modèle
        model = self.supplierTable.model
        for row_data in sample_data:
            model.insertRows(len(model._data), 1)
            for col, value in enumerate(row_data):
                model.setData(model.index(len(model._data) - 1, col), value)

    def openSupplierForm(self):
        """Ouvre le formulaire d'ajout de fournisseur."""
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter un fournisseur")
        dialog.setMinimumWidth(500)

        layout = QVBoxLayout(dialog)

        supplierForm = SupplierForm(parent=dialog)
        supplierForm.formSubmitted.connect(lambda data: self.saveSupplier(data, dialog))
        supplierForm.formCancelled.connect(dialog.reject)

        layout.addWidget(supplierForm)

        dialog.exec_()

    def editSupplier(self, row, data):
        """Ouvre le formulaire d'édition de fournisseur."""
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier un fournisseur")
        dialog.setMinimumWidth(500)

        layout = QVBoxLayout(dialog)

        supplierForm = SupplierForm(parent=dialog)

        # Pré-remplir le formulaire avec les données existantes
        supplier_data = {
            "code": data[0],
            "name": data[1],
            "address": data[2],
            "phone": data[3],
            "email": data[4],
            "balance": data[5].replace(" DA", "").replace(" ", "").replace(",", ".")
        }
        supplierForm.loadData(supplier_data)

        supplierForm.formSubmitted.connect(lambda data: self.updateSupplier(row, data, dialog))
        supplierForm.formCancelled.connect(dialog.reject)

        layout.addWidget(supplierForm)

        dialog.exec_()

    def saveSupplier(self, data, dialog):
        """Enregistre un nouveau fournisseur."""
        # Formater les données pour l'affichage
        row_data = [
            data["code"],
            data["name"],
            data["address"],
            data["phone"],
            data["email"],
            f"{float(data['balance']):,.2f} DA".replace(",", " ").replace(".", ",")
        ]

        # Ajouter au modèle
        model = self.supplierTable.model
        model.insertRows(len(model._data), 1)
        for col, value in enumerate(row_data):
            model.setData(model.index(len(model._data) - 1, col), value)

        # Afficher une notification de succès
        InfoBar.success(
            title="Fournisseur ajouté",
            content=f"Le fournisseur {data['name']} a été ajouté avec succès.",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

        dialog.accept()

    def updateSupplier(self, row, data, dialog):
        """Met à jour un fournisseur existant."""
        # Formater les données pour l'affichage
        row_data = [
            data["code"],
            data["name"],
            data["address"],
            data["phone"],
            data["email"],
            f"{float(data['balance']):,.2f} DA".replace(",", " ").replace(".", ",")
        ]

        # Mettre à jour le modèle
        model = self.supplierTable.model
        for col, value in enumerate(row_data):
            source_index = self.supplierTable.proxyModel.mapToSource(
                self.supplierTable.proxyModel.index(row, col)
            )
            model.setData(source_index, value)

        # Afficher une notification de succès
        InfoBar.success(
            title="Fournisseur modifié",
            content=f"Le fournisseur {data['name']} a été modifié avec succès.",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

        dialog.accept()

    def deleteSupplier(self, row, data):
        """Supprime un fournisseur."""
        supplier_name = data[1]
        
        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer le fournisseur '{supplier_name}' ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Supprimer du modèle
            source_index = self.supplierTable.proxyModel.mapToSource(
                self.supplierTable.proxyModel.index(row, 0)
            )
            self.supplierTable.model.removeRows(source_index.row(), 1)

            # Afficher une notification de succès
            InfoBar.success(
                title="Fournisseur supprimé",
                content=f"Le fournisseur {supplier_name} a été supprimé avec succès.",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )

    def updateStyle(self):
        """Met à jour le style en fonction du thème actuel"""
        self.setStyleSheet(get_clean_style())

    @pyqtSlot(object)
    def onThemeChanged(self, _):
        """Appelé lorsque le thème change"""
        self.updateStyle()
