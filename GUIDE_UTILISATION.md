# 📖 Guide d'Utilisation - GSCOM

## 🚀 Démarrage Rapide

### 1. Lancement de l'Application
```bash
python main.py
```

### 2. Connexion
- **Interface moderne** avec effets glassmorphism
- **Identifiants par défaut**: `admin` / `admin`
- **Fonctionnalités**:
  - Animations d'entrée fluides
  - Particules flottantes
  - Basculement de thème
  - Validation en temps réel

## 🏠 Dashboard Principal

### 📊 Cartes de Statistiques
- **Ventes du jour**: Chiffre d'affaires quotidien
- **Commandes**: Nombre de commandes en cours
- **Nouveaux clients**: Clients ajoutés récemment
- **Stock critique**: Produits en rupture

**💡 Astuce**: Cliquez sur les cartes pour accéder aux détails

### 📈 Graphiques et Activités
- **Graphique des ventes**: Évolution temporelle (à implémenter)
- **Flux d'activité**: Actions récentes en temps réel
- **Mise à jour automatique**: Toutes les 30 secondes

### ⚡ Actions Rapides
- **Nouvelle vente**: Créer une vente rapidement
- **Nouveau client**: Ajouter un client
- **Gestion stock**: Accéder à l'inventaire
- **Rapports**: Générer des rapports

## 📋 Modules Principaux

### 🛍️ Catalogue Produits
- Liste complète des produits
- Recherche et filtres avancés
- Ajout/modification/suppression
- Gestion des catégories

### 👥 Gestion Clients
- Base de données clients
- Historique des achats
- Informations de contact
- Suivi des créances

### 🏢 Gestion Fournisseurs
- **Nouveau module** avec interface moderne
- Informations complètes des fournisseurs
- Gestion des contacts
- Suivi des commandes

### 💰 Module Ventes
- **Liste des ventes**: Vue d'ensemble des transactions
- **Factures**: Gestion des factures
- **Devis**: Création et suivi des devis
- Export et impression

### 📦 Gestion Stock
- Mouvements de stock en temps réel
- Alertes de stock critique
- Inventaire physique
- Valorisation du stock

### 📊 Rapports
- **Types disponibles**:
  - Rapport de ventes
  - Rapport d'inventaire
  - Rapport financier
  - Rapport clients/fournisseurs
- **Formats**: PDF, Excel, CSV, HTML
- **Périodes personnalisables**
- **Aperçu en temps réel**

## ⚙️ Paramètres

### 🏢 Informations Entreprise
- Nom et adresse de l'entreprise
- Coordonnées de contact
- Configuration générale

### 🎨 Apparence
- **10 thèmes modernes** disponibles:
  - **Sombres**: Dark Blue, Dark Purple, Dark Green, Cyberpunk, Ocean, Sunset, Forest
  - **Clairs**: Light Blue, Light Purple, Light Green
- Taille de police personnalisable
- Effets visuels configurables

### 🗄️ Base de Données
- Configuration de la base de données
- Sauvegarde automatique
- Gestion des fichiers

### 🔒 Sécurité
- Verrouillage automatique
- Journalisation des actions
- Gestion des sessions

## 🎨 Personnalisation des Thèmes

### 🌙 Thèmes Sombres
- **Dark Blue**: Thème principal moderne
- **Dark Purple**: Élégant et professionnel
- **Dark Green**: Naturel et apaisant
- **Cyberpunk**: Futuriste avec néons
- **Ocean**: Bleu profond et apaisant
- **Sunset**: Couleurs chaudes
- **Forest**: Vert naturel

### ☀️ Thèmes Clairs
- **Light Blue**: Clair et professionnel
- **Light Purple**: Doux et moderne
- **Light Green**: Frais et naturel

### 🎛️ Changement de Thème
1. Accéder aux **Paramètres**
2. Onglet **Apparence**
3. Sélectionner un thème dans l'aperçu
4. Cliquer sur **Appliquer**

## 🔔 Système de Notifications

### 📱 Types de Notifications
- **Succès** (vert): Actions réussies
- **Erreur** (rouge): Problèmes à résoudre
- **Avertissement** (orange): Attention requise
- **Information** (bleu): Informations générales

### ⚡ Fonctionnalités
- Apparition animée depuis la droite
- Fermeture automatique ou manuelle
- Positionnement intelligent
- Limite de 5 notifications simultanées

## 🔧 Fonctionnalités Avancées

### 🔍 Recherche Globale
- Recherche dans tous les modules
- Filtres avancés
- Résultats en temps réel

### 📤 Export de Données
- Formats multiples (CSV, Excel, PDF)
- Sélection personnalisée
- Planification automatique

### 🔄 Synchronisation
- Mise à jour automatique des données
- Sauvegarde en temps réel
- Récupération d'erreurs

## 🎯 Raccourcis Clavier

### ⌨️ Navigation
- **Ctrl + H**: Retour au dashboard
- **Ctrl + N**: Nouvelle vente
- **Ctrl + F**: Recherche
- **Ctrl + S**: Sauvegarder
- **F5**: Actualiser les données

### 🎨 Interface
- **Ctrl + T**: Changer de thème
- **F11**: Mode plein écran
- **Ctrl + +/-**: Zoom

## 🆘 Dépannage

### ❌ Problèmes Courants

#### Interface ne s'affiche pas
- Vérifier les dépendances PyQt5 et QFluentWidgets
- Redémarrer l'application
- Vérifier les logs d'erreur

#### Thème ne s'applique pas
- Redémarrer l'application
- Vérifier les paramètres de thème
- Réinitialiser aux paramètres par défaut

#### Base de données inaccessible
- Vérifier les permissions de fichier
- Contrôler l'espace disque
- Restaurer depuis une sauvegarde

### 📞 Support
- Consulter les logs dans le terminal
- Vérifier le fichier `AMELIORATIONS.md`
- Contacter l'administrateur système

## 🎓 Conseils d'Utilisation

### 💡 Bonnes Pratiques
1. **Sauvegarde régulière** des données
2. **Mise à jour** des informations clients
3. **Vérification** du stock critique
4. **Génération** de rapports périodiques
5. **Formation** des utilisateurs

### 🚀 Optimisation
- Utiliser les raccourcis clavier
- Personnaliser l'interface selon vos besoins
- Configurer les notifications importantes
- Organiser les données régulièrement

## 📈 Évolution Future

L'application GSCOM continue d'évoluer avec de nouvelles fonctionnalités prévues :
- Graphiques avancés
- Mode hors ligne
- Application mobile
- API REST
- Plugins personnalisés

---

**🎉 Profitez de votre nouvelle interface GSCOM moderne !**
