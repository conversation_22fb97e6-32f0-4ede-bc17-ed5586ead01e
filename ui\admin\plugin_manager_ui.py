"""
Interface de gestion des plugins pour GSCOM.
Permet de gérer, installer et configurer les plugins.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QTabWidget, QTextEdit, QProgressBar,
    QFileDialog, QMessageBox, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon

from qfluentwidgets import (
    FluentIcon, IconWidget, PushButton, LineEdit, ComboBox,
    CardWidget, InfoBar, InfoBarPosition, SwitchButton, ProgressBar
)
from ui.themes.modern_themes import modern_theme_manager
from core.plugin_system import plugin_manager, PluginInterface, UIPluginInterface
from datetime import datetime
import os


class PluginCard(CardWidget):
    """Carte d'affichage d'un plugin."""

    toggleRequested = pyqtSignal(str, bool)  # plugin_name, enable
    configureRequested = pyqtSignal(str)  # plugin_name
    uninstallRequested = pyqtSignal(str)  # plugin_name

    def __init__(self, plugin_name: str, plugin_info: dict, parent=None):
        super().__init__(parent)
        self.plugin_name = plugin_name
        self.plugin_info = plugin_info
        self.setupUI()

    def setupUI(self):
        """Configure l'interface de la carte."""
        self.setFixedHeight(200)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # En-tête avec nom et version
        header_layout = QHBoxLayout()

        # Icône du plugin
        icon_widget = IconWidget(FluentIcon.APPLICATION, self)
        icon_widget.setFixedSize(32, 32)

        # Informations principales
        info_layout = QVBoxLayout()

        name_label = QLabel(self.plugin_info.get("name", self.plugin_name))
        name_label.setObjectName("pluginName")
        name_label.setFont(QFont("Segoe UI", 14, QFont.Bold))

        version_label = QLabel(f"Version {self.plugin_info.get('version', '1.0.0')}")
        version_label.setObjectName("pluginVersion")
        version_label.setFont(QFont("Segoe UI", 10))

        info_layout.addWidget(name_label)
        info_layout.addWidget(version_label)

        # État du plugin
        status_layout = QVBoxLayout()

        loaded_status = "✅ Chargé" if self.plugin_info.get("loaded", False) else "⭕ Non chargé"
        enabled_status = "🟢 Activé" if self.plugin_info.get("enabled", False) else "🔴 Désactivé"

        loaded_label = QLabel(loaded_status)
        enabled_label = QLabel(enabled_status)

        status_layout.addWidget(loaded_label)
        status_layout.addWidget(enabled_label)

        header_layout.addWidget(icon_widget)
        header_layout.addLayout(info_layout, 1)
        header_layout.addLayout(status_layout)

        # Description
        description = self.plugin_info.get("description", "Aucune description disponible")
        desc_label = QLabel(description)
        desc_label.setObjectName("pluginDescription")
        desc_label.setWordWrap(True)
        desc_label.setMaximumHeight(40)

        # Auteur
        author_label = QLabel(f"Par: {self.plugin_info.get('author', 'Inconnu')}")
        author_label.setObjectName("pluginAuthor")
        author_label.setFont(QFont("Segoe UI", 9))

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        # Switch d'activation
        self.enable_switch = SwitchButton()
        self.enable_switch.setChecked(self.plugin_info.get("enabled", False))
        self.enable_switch.checkedChanged.connect(
            lambda checked: self.toggleRequested.emit(self.plugin_name, checked)
        )

        enable_label = QLabel("Activé")

        # Bouton de configuration
        config_btn = PushButton("Configurer")
        config_btn.setIcon(FluentIcon.SETTING.icon())
        config_btn.clicked.connect(lambda: self.configureRequested.emit(self.plugin_name))
        config_btn.setEnabled(self.plugin_info.get("loaded", False))

        # Bouton de désinstallation
        uninstall_btn = PushButton("Désinstaller")
        uninstall_btn.setIcon(FluentIcon.DELETE.icon())
        uninstall_btn.clicked.connect(lambda: self.uninstallRequested.emit(self.plugin_name))

        buttons_layout.addWidget(enable_label)
        buttons_layout.addWidget(self.enable_switch)
        buttons_layout.addStretch()
        buttons_layout.addWidget(config_btn)
        buttons_layout.addWidget(uninstall_btn)

        layout.addLayout(header_layout)
        layout.addWidget(desc_label)
        layout.addWidget(author_label)
        layout.addLayout(buttons_layout)

        # Style
        self.apply_style()

    def apply_style(self):
        """Applique le style à la carte."""
        colors = modern_theme_manager.get_colors()

        # Couleur de bordure selon l'état
        if self.plugin_info.get("loaded", False):
            border_color = "#10B981"  # Vert pour chargé
        elif self.plugin_info.get("enabled", False):
            border_color = "#F59E0B"  # Orange pour activé mais non chargé
        else:
            border_color = colors['border']  # Couleur normale

        self.setStyleSheet(f"""
            PluginCard {{
                background: {colors['surface']};
                border: 1px solid {border_color};
                border-left: 4px solid {border_color};
                border-radius: 12px;
            }}
            QLabel#pluginName {{
                color: {colors['text']};
            }}
            QLabel#pluginVersion {{
                color: {colors['text_secondary']};
            }}
            QLabel#pluginDescription {{
                color: {colors['text_secondary']};
            }}
            QLabel#pluginAuthor {{
                color: {colors['text_secondary']};
                font-style: italic;
            }}
        """)


class PluginManagerUI(QWidget):
    """Interface de gestion des plugins."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.plugin_cards = {}
        self.setupUI()
        self.load_plugins()

        # Connecter les signaux du gestionnaire de plugins
        plugin_manager.pluginLoaded.connect(self.on_plugin_loaded)
        plugin_manager.pluginUnloaded.connect(self.on_plugin_unloaded)
        plugin_manager.pluginError.connect(self.on_plugin_error)

    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête
        header_layout = self.createHeader()
        layout.addLayout(header_layout)

        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)

        # Panneau gauche - Liste des plugins
        left_panel = self.createPluginsPanel()
        splitter.addWidget(left_panel)

        # Panneau droit - Détails et logs
        right_panel = self.createDetailsPanel()
        splitter.addWidget(right_panel)

        # Proportions
        splitter.setSizes([600, 400])

        layout.addWidget(splitter)

    def createHeader(self):
        """Crée l'en-tête."""
        layout = QHBoxLayout()

        # Titre
        title_label = QLabel("Gestionnaire de Plugins")
        title_label.setFont(QFont("Segoe UI", 24, QFont.Bold))

        subtitle_label = QLabel("Gérez les extensions et fonctionnalités")
        subtitle_label.setFont(QFont("Segoe UI", 12))
        subtitle_label.setStyleSheet("color: #6B7280;")

        title_layout = QVBoxLayout()
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        # Boutons d'action
        refresh_btn = PushButton("Actualiser")
        refresh_btn.setIcon(FluentIcon.SYNC.icon())
        refresh_btn.clicked.connect(self.refresh_plugins)

        install_btn = PushButton("Installer Plugin")
        install_btn.setIcon(FluentIcon.ADD.icon())
        install_btn.clicked.connect(self.install_plugin)

        store_btn = PushButton("Magasin")
        store_btn.setIcon(FluentIcon.MARKET.icon())
        store_btn.clicked.connect(self.open_plugin_store)

        layout.addLayout(title_layout)
        layout.addStretch()
        layout.addWidget(refresh_btn)
        layout.addWidget(install_btn)
        layout.addWidget(store_btn)

        return layout

    def createPluginsPanel(self):
        """Crée le panneau des plugins."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Filtres
        filters_layout = QHBoxLayout()

        filter_label = QLabel("Filtrer:")
        self.category_filter = ComboBox()
        self.category_filter.addItems([
            "Tous", "Interface", "Données", "Rapports", "Outils"
        ])
        self.category_filter.currentTextChanged.connect(self.filter_plugins)

        self.status_filter = ComboBox()
        self.status_filter.addItems([
            "Tous", "Activés", "Désactivés", "Chargés", "Non chargés"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_plugins)

        filters_layout.addWidget(filter_label)
        filters_layout.addWidget(self.category_filter)
        filters_layout.addWidget(self.status_filter)
        filters_layout.addStretch()

        # Zone de défilement pour les cartes de plugins
        from PyQt5.QtWidgets import QScrollArea

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.plugins_widget = QWidget()
        self.plugins_layout = QVBoxLayout(self.plugins_widget)
        self.plugins_layout.setSpacing(10)

        scroll_area.setWidget(self.plugins_widget)

        layout.addLayout(filters_layout)
        layout.addWidget(scroll_area)

        return widget

    def createDetailsPanel(self):
        """Crée le panneau de détails."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Onglets
        tabs = QTabWidget()

        # Onglet Logs
        logs_tab = QWidget()
        logs_layout = QVBoxLayout(logs_tab)

        logs_label = QLabel("Journal des Plugins")
        logs_label.setFont(QFont("Segoe UI", 14, QFont.Bold))

        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setMaximumHeight(200)

        clear_logs_btn = PushButton("Effacer")
        clear_logs_btn.setIcon(FluentIcon.DELETE.icon())
        clear_logs_btn.clicked.connect(self.clear_logs)

        logs_layout.addWidget(logs_label)
        logs_layout.addWidget(self.logs_text)
        logs_layout.addWidget(clear_logs_btn)

        # Onglet Statistiques
        stats_tab = self.createStatsTab()

        tabs.addTab(logs_tab, "📋 Logs")
        tabs.addTab(stats_tab, "📊 Statistiques")

        layout.addWidget(tabs)

        return widget

    def createStatsTab(self):
        """Crée l'onglet des statistiques."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        stats_label = QLabel("Statistiques des Plugins")
        stats_label.setFont(QFont("Segoe UI", 14, QFont.Bold))

        # Métriques
        metrics_layout = QGridLayout()

        self.total_plugins_label = QLabel("0")
        self.loaded_plugins_label = QLabel("0")
        self.enabled_plugins_label = QLabel("0")
        self.errors_label = QLabel("0")

        metrics_layout.addWidget(QLabel("Total:"), 0, 0)
        metrics_layout.addWidget(self.total_plugins_label, 0, 1)
        metrics_layout.addWidget(QLabel("Chargés:"), 1, 0)
        metrics_layout.addWidget(self.loaded_plugins_label, 1, 1)
        metrics_layout.addWidget(QLabel("Activés:"), 2, 0)
        metrics_layout.addWidget(self.enabled_plugins_label, 2, 1)
        metrics_layout.addWidget(QLabel("Erreurs:"), 3, 0)
        metrics_layout.addWidget(self.errors_label, 3, 1)

        layout.addWidget(stats_label)
        layout.addLayout(metrics_layout)
        layout.addStretch()

        return widget

    def load_plugins(self):
        """Charge la liste des plugins."""
        # Découvrir les plugins
        plugins = plugin_manager.discover_plugins()

        # Effacer les cartes existantes
        self.clear_plugin_cards()

        # Créer les cartes pour chaque plugin
        for plugin_name in plugins:
            plugin_info = plugin_manager.get_plugin_info(plugin_name)
            self.add_plugin_card(plugin_name, plugin_info)

        # Mettre à jour les statistiques
        self.update_statistics()

        self.log_message(f"{len(plugins)} plugins découverts")

    def add_plugin_card(self, plugin_name: str, plugin_info: dict):
        """Ajoute une carte de plugin."""
        card = PluginCard(plugin_name, plugin_info)
        card.toggleRequested.connect(self.toggle_plugin)
        card.configureRequested.connect(self.configure_plugin)
        card.uninstallRequested.connect(self.uninstall_plugin)

        self.plugins_layout.addWidget(card)
        self.plugin_cards[plugin_name] = card

    def clear_plugin_cards(self):
        """Efface toutes les cartes de plugins."""
        while self.plugins_layout.count():
            child = self.plugins_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        self.plugin_cards.clear()

    def refresh_plugins(self):
        """Actualise la liste des plugins."""
        self.load_plugins()
        self.log_message("Liste des plugins actualisée")

    def filter_plugins(self):
        """Filtre les plugins selon les critères."""
        category = self.category_filter.currentText()
        status = self.status_filter.currentText()

        for plugin_name, card in self.plugin_cards.items():
            show_card = True

            # Filtrer par statut
            if status != "Tous":
                plugin_info = plugin_manager.get_plugin_info(plugin_name)
                if status == "Activés" and not plugin_info.get("enabled", False):
                    show_card = False
                elif status == "Désactivés" and plugin_info.get("enabled", False):
                    show_card = False
                elif status == "Chargés" and not plugin_info.get("loaded", False):
                    show_card = False
                elif status == "Non chargés" and plugin_info.get("loaded", False):
                    show_card = False

            card.setVisible(show_card)

    def toggle_plugin(self, plugin_name: str, enable: bool):
        """Active/désactive un plugin."""
        if enable:
            success = plugin_manager.enable_plugin(plugin_name)
            if success:
                plugin_manager.load_plugin(plugin_name)
        else:
            plugin_manager.disable_plugin(plugin_name)

        # Actualiser la carte
        plugin_info = plugin_manager.get_plugin_info(plugin_name)
        if plugin_name in self.plugin_cards:
            # Recréer la carte avec les nouvelles informations
            old_card = self.plugin_cards[plugin_name]
            index = self.plugins_layout.indexOf(old_card)
            old_card.deleteLater()

            new_card = PluginCard(plugin_name, plugin_info)
            new_card.toggleRequested.connect(self.toggle_plugin)
            new_card.configureRequested.connect(self.configure_plugin)
            new_card.uninstallRequested.connect(self.uninstall_plugin)

            self.plugins_layout.insertWidget(index, new_card)
            self.plugin_cards[plugin_name] = new_card

        self.update_statistics()

    def configure_plugin(self, plugin_name: str):
        """Configure un plugin."""
        self.log_message(f"Configuration du plugin {plugin_name}")

        InfoBar.info(
            title="Configuration",
            content=f"Configuration du plugin {plugin_name} (à implémenter)",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

    def uninstall_plugin(self, plugin_name: str):
        """Désinstalle un plugin."""
        reply = QMessageBox.question(
            self,
            "Confirmer la désinstallation",
            f"Êtes-vous sûr de vouloir désinstaller le plugin '{plugin_name}' ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Décharger d'abord le plugin
            plugin_manager.unload_plugin(plugin_name)

            # TODO: Implémenter la désinstallation réelle
            self.log_message(f"Plugin {plugin_name} désinstallé")

            # Actualiser la liste
            self.refresh_plugins()

    def install_plugin(self):
        """Installe un nouveau plugin."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Sélectionner un plugin",
            "",
            "Archives ZIP (*.zip);;Tous les fichiers (*)"
        )

        if file_path:
            # TODO: Implémenter l'installation de plugins
            self.log_message(f"Installation du plugin depuis {file_path}")

            InfoBar.info(
                title="Installation",
                content="Installation de plugins à implémenter",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )

    def open_plugin_store(self):
        """Ouvre le magasin de plugins."""
        self.log_message("Ouverture du magasin de plugins")

        InfoBar.info(
            title="Magasin",
            content="Magasin de plugins à implémenter",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

    def update_statistics(self):
        """Met à jour les statistiques."""
        plugins = plugin_manager.discover_plugins()
        total = len(plugins)

        loaded = sum(1 for p in plugins if plugin_manager.get_plugin_info(p).get("loaded", False))
        enabled = sum(1 for p in plugins if plugin_manager.get_plugin_info(p).get("enabled", False))

        self.total_plugins_label.setText(str(total))
        self.loaded_plugins_label.setText(str(loaded))
        self.enabled_plugins_label.setText(str(enabled))

    def log_message(self, message: str):
        """Ajoute un message au journal."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logs_text.append(f"[{timestamp}] {message}")

    def clear_logs(self):
        """Efface le journal."""
        self.logs_text.clear()

    @pyqtSlot(str)
    def on_plugin_loaded(self, plugin_name: str):
        """Appelé quand un plugin est chargé."""
        self.log_message(f"✅ Plugin {plugin_name} chargé")
        self.refresh_plugins()

    @pyqtSlot(str)
    def on_plugin_unloaded(self, plugin_name: str):
        """Appelé quand un plugin est déchargé."""
        self.log_message(f"⭕ Plugin {plugin_name} déchargé")
        self.refresh_plugins()

    @pyqtSlot(str, str)
    def on_plugin_error(self, plugin_name: str, error_message: str):
        """Appelé quand il y a une erreur de plugin."""
        self.log_message(f"❌ Erreur plugin {plugin_name}: {error_message}")

        # Incrémenter le compteur d'erreurs
        current_errors = int(self.errors_label.text())
        self.errors_label.setText(str(current_errors + 1))
