"""
Système de monitoring et performance en temps réel pour GSCOM.
Surveillance des ressources système et performance de l'application.
"""
try:
    import psutil
except ImportError:
    # Utiliser notre mock si psutil n'est pas disponible
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'utils'))
    import psutil_mock as psutil
import time
import threading
from datetime import datetime, timedelta
from collections import deque
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QProgressBar, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QTextEdit
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QObject
from PyQt5.QtGui import <PERSON><PERSON>ont, Q<PERSON><PERSON>ter, QColor, QLinearGradient, QBrush

from qfluentwidgets import FluentIcon, IconWidget, PushButton, CardWidget, InfoBar, InfoBarPosition
from ui.themes.modern_themes import modern_theme_manager
from ui.components.charts import AnimatedChart
import json


class SystemMetrics:
    """Métriques système."""

    def __init__(self):
        self.cpu_percent = 0.0
        self.memory_percent = 0.0
        self.memory_used = 0
        self.memory_total = 0
        self.disk_percent = 0.0
        self.disk_used = 0
        self.disk_total = 0
        self.network_sent = 0
        self.network_recv = 0
        self.process_count = 0
        self.timestamp = datetime.now()

    def update(self):
        """Met à jour les métriques."""
        self.timestamp = datetime.now()

        # CPU
        self.cpu_percent = psutil.cpu_percent(interval=0.1)

        # Mémoire
        memory = psutil.virtual_memory()
        self.memory_percent = memory.percent
        self.memory_used = memory.used
        self.memory_total = memory.total

        # Disque
        disk = psutil.disk_usage('/')
        self.disk_percent = disk.percent if hasattr(disk, 'percent') else (disk.used / disk.total * 100)
        self.disk_used = disk.used
        self.disk_total = disk.total

        # Réseau
        network = psutil.net_io_counters()
        self.network_sent = network.bytes_sent
        self.network_recv = network.bytes_recv

        # Processus
        self.process_count = len(psutil.pids())

    def to_dict(self):
        """Convertit en dictionnaire."""
        return {
            'cpu_percent': self.cpu_percent,
            'memory_percent': self.memory_percent,
            'memory_used': self.memory_used,
            'memory_total': self.memory_total,
            'disk_percent': self.disk_percent,
            'disk_used': self.disk_used,
            'disk_total': self.disk_total,
            'network_sent': self.network_sent,
            'network_recv': self.network_recv,
            'process_count': self.process_count,
            'timestamp': self.timestamp.isoformat()
        }


class PerformanceMonitor(QThread):
    """Moniteur de performance en arrière-plan."""

    metricsUpdated = pyqtSignal(SystemMetrics)
    alertTriggered = pyqtSignal(str, str)  # type, message

    def __init__(self):
        super().__init__()
        self.running = False
        self.update_interval = 2  # secondes
        self.metrics_history = deque(maxlen=300)  # 10 minutes à 2s d'intervalle

        # Seuils d'alerte
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.disk_threshold = 90.0

    def start_monitoring(self):
        """Démarre le monitoring."""
        self.running = True
        self.start()

    def stop_monitoring(self):
        """Arrête le monitoring."""
        self.running = False
        self.quit()
        self.wait()

    def run(self):
        """Boucle principale de monitoring."""
        while self.running:
            metrics = SystemMetrics()
            metrics.update()

            # Ajouter à l'historique
            self.metrics_history.append(metrics)

            # Vérifier les seuils d'alerte
            self.check_alerts(metrics)

            # Émettre le signal
            self.metricsUpdated.emit(metrics)

            # Attendre avant la prochaine mesure
            time.sleep(self.update_interval)

    def check_alerts(self, metrics: SystemMetrics):
        """Vérifie les seuils d'alerte."""
        if metrics.cpu_percent > self.cpu_threshold:
            self.alertTriggered.emit(
                "CPU",
                f"Utilisation CPU élevée: {metrics.cpu_percent:.1f}%"
            )

        if metrics.memory_percent > self.memory_threshold:
            self.alertTriggered.emit(
                "Memory",
                f"Utilisation mémoire élevée: {metrics.memory_percent:.1f}%"
            )

        if metrics.disk_percent > self.disk_threshold:
            self.alertTriggered.emit(
                "Disk",
                f"Espace disque faible: {metrics.disk_percent:.1f}%"
            )

    def get_metrics_history(self, duration_minutes=10):
        """Récupère l'historique des métriques."""
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        return [m for m in self.metrics_history if m.timestamp > cutoff_time]


class MetricCard(CardWidget):
    """Carte d'affichage d'une métrique."""

    def __init__(self, title, icon, color="#3B82F6", parent=None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self.color = color
        self.value = 0.0
        self.unit = "%"
        self.setupUI()

    def setupUI(self):
        """Configure l'interface de la carte."""
        self.setFixedSize(200, 120)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(8)

        # En-tête avec icône et titre
        header_layout = QHBoxLayout()

        icon_widget = IconWidget(self.icon, self)
        icon_widget.setFixedSize(24, 24)

        title_label = QLabel(self.title)
        title_label.setObjectName("metricTitle")
        title_label.setFont(QFont("Segoe UI", 11, QFont.Bold))

        header_layout.addWidget(icon_widget)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Valeur principale
        self.value_label = QLabel("0%")
        self.value_label.setObjectName("metricValue")
        self.value_label.setFont(QFont("Segoe UI", 20, QFont.Bold))
        self.value_label.setAlignment(Qt.AlignCenter)

        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(6)

        layout.addLayout(header_layout)
        layout.addWidget(self.value_label)
        layout.addWidget(self.progress_bar)

        # Style
        self.apply_style()

    def update_value(self, value, unit="%"):
        """Met à jour la valeur affichée."""
        self.value = value
        self.unit = unit

        if unit == "%":
            self.value_label.setText(f"{value:.1f}%")
            self.progress_bar.setValue(int(value))
        else:
            self.value_label.setText(f"{value:.1f}{unit}")

        # Changer la couleur selon la valeur
        if value > 80:
            color = "#EF4444"  # Rouge
        elif value > 60:
            color = "#F59E0B"  # Orange
        else:
            color = self.color  # Couleur normale

        self.update_color(color)

    def update_color(self, color):
        """Met à jour la couleur de la carte."""
        self.color = color
        self.apply_style()

        # Mettre à jour la barre de progression
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 3px;
                background-color: rgba(255, 255, 255, 0.1);
            }}
            QProgressBar::chunk {{
                background: {color};
                border-radius: 3px;
            }}
        """)

    def apply_style(self):
        """Applique le style à la carte."""
        colors = modern_theme_manager.get_colors()

        self.setStyleSheet(f"""
            MetricCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}15,
                    stop:1 {self.color}08);
                border: 1px solid {self.color}30;
                border-radius: 12px;
            }}
            QLabel#metricTitle {{
                color: {colors['text_secondary']};
            }}
            QLabel#metricValue {{
                color: {self.color};
            }}
        """)


class ProcessTable(QTableWidget):
    """Tableau des processus système."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_processes)
        self.update_timer.start(5000)  # Mise à jour toutes les 5 secondes

    def setupUI(self):
        """Configure le tableau."""
        self.setColumnCount(5)
        self.setHorizontalHeaderLabels([
            "PID", "Nom", "CPU %", "Mémoire", "État"
        ])

        # Configuration des colonnes
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # PID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # CPU
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Mémoire
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # État

        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSortingEnabled(True)

    def update_processes(self):
        """Met à jour la liste des processus."""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'status']):
                try:
                    info = proc.info
                    memory_mb = info['memory_info'].rss / 1024 / 1024 if info['memory_info'] else 0
                    processes.append({
                        'pid': info['pid'],
                        'name': info['name'] or 'N/A',
                        'cpu_percent': info['cpu_percent'] or 0,
                        'memory_mb': memory_mb,
                        'status': info['status'] or 'unknown'
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Trier par utilisation CPU décroissante
            processes.sort(key=lambda x: x['cpu_percent'], reverse=True)

            # Limiter à 20 processus
            processes = processes[:20]

            # Mettre à jour le tableau
            self.setRowCount(len(processes))

            for row, proc in enumerate(processes):
                self.setItem(row, 0, QTableWidgetItem(str(proc['pid'])))
                self.setItem(row, 1, QTableWidgetItem(proc['name']))
                self.setItem(row, 2, QTableWidgetItem(f"{proc['cpu_percent']:.1f}"))
                self.setItem(row, 3, QTableWidgetItem(f"{proc['memory_mb']:.1f} MB"))
                self.setItem(row, 4, QTableWidgetItem(proc['status']))

        except Exception as e:
            print(f"Erreur lors de la mise à jour des processus: {e}")


class SystemMonitor(QWidget):
    """Interface de monitoring système."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitor = PerformanceMonitor()
        self.alerts_count = 0
        self.setupUI()
        self.start_monitoring()

    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête
        header_layout = self.createHeader()
        layout.addLayout(header_layout)

        # Métriques principales
        metrics_layout = self.createMetricsSection()
        layout.addLayout(metrics_layout)

        # Onglets de détails
        tabs = QTabWidget()

        # Onglet Graphiques
        charts_tab = self.createChartsTab()
        tabs.addTab(charts_tab, "📊 Graphiques")

        # Onglet Processus
        processes_tab = self.createProcessesTab()
        tabs.addTab(processes_tab, "⚙️ Processus")

        # Onglet Alertes
        alerts_tab = self.createAlertsTab()
        tabs.addTab(alerts_tab, "🚨 Alertes")

        layout.addWidget(tabs)

    def createHeader(self):
        """Crée l'en-tête."""
        layout = QHBoxLayout()

        # Titre
        title_label = QLabel("Monitoring Système")
        title_label.setFont(QFont("Segoe UI", 24, QFont.Bold))

        subtitle_label = QLabel("Surveillance en temps réel des performances")
        subtitle_label.setFont(QFont("Segoe UI", 12))
        subtitle_label.setStyleSheet("color: #6B7280;")

        title_layout = QVBoxLayout()
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        # Indicateur de statut
        self.status_label = QLabel("🟢 Système normal")
        self.status_label.setFont(QFont("Segoe UI", 12, QFont.Bold))

        # Boutons de contrôle
        export_btn = PushButton("Exporter Rapport")
        export_btn.setIcon(FluentIcon.DOWNLOAD.icon())
        export_btn.clicked.connect(self.export_report)

        settings_btn = PushButton("Paramètres")
        settings_btn.setIcon(FluentIcon.SETTING.icon())
        settings_btn.clicked.connect(self.open_settings)

        layout.addLayout(title_layout)
        layout.addStretch()
        layout.addWidget(self.status_label)
        layout.addWidget(export_btn)
        layout.addWidget(settings_btn)

        return layout

    def createMetricsSection(self):
        """Crée la section des métriques."""
        layout = QHBoxLayout()
        layout.setSpacing(15)

        # Cartes de métriques
        self.cpu_card = MetricCard("CPU", FluentIcon.SPEED_HIGH, "#3B82F6")
        self.memory_card = MetricCard("Mémoire", FluentIcon.MEMORY, "#10B981")
        self.disk_card = MetricCard("Disque", FluentIcon.HARD_DRIVE, "#F59E0B")
        self.network_card = MetricCard("Réseau", FluentIcon.WIFI, "#8B5CF6")

        layout.addWidget(self.cpu_card)
        layout.addWidget(self.memory_card)
        layout.addWidget(self.disk_card)
        layout.addWidget(self.network_card)
        layout.addStretch()

        return layout

    def createChartsTab(self):
        """Crée l'onglet des graphiques."""
        widget = QWidget()
        layout = QGridLayout(widget)

        # Graphiques de performance
        self.cpu_chart = AnimatedChart("Utilisation CPU")
        self.memory_chart = AnimatedChart("Utilisation Mémoire")

        layout.addWidget(self.cpu_chart, 0, 0)
        layout.addWidget(self.memory_chart, 0, 1)

        return widget

    def createProcessesTab(self):
        """Crée l'onglet des processus."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Tableau des processus
        self.process_table = ProcessTable()
        layout.addWidget(self.process_table)

        return widget

    def createAlertsTab(self):
        """Crée l'onglet des alertes."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Journal des alertes
        alerts_label = QLabel("Journal des Alertes")
        alerts_label.setFont(QFont("Segoe UI", 16, QFont.Bold))

        self.alerts_text = QTextEdit()
        self.alerts_text.setReadOnly(True)

        # Bouton pour effacer les alertes
        clear_btn = PushButton("Effacer")
        clear_btn.setIcon(FluentIcon.DELETE.icon())
        clear_btn.clicked.connect(self.clear_alerts)

        layout.addWidget(alerts_label)
        layout.addWidget(self.alerts_text)
        layout.addWidget(clear_btn)

        return widget

    def start_monitoring(self):
        """Démarre le monitoring."""
        # Connecter les signaux
        self.monitor.metricsUpdated.connect(self.update_metrics)
        self.monitor.alertTriggered.connect(self.handle_alert)

        # Démarrer le monitoring
        self.monitor.start_monitoring()

    def update_metrics(self, metrics: SystemMetrics):
        """Met à jour l'affichage des métriques."""
        # Mettre à jour les cartes
        self.cpu_card.update_value(metrics.cpu_percent)
        self.memory_card.update_value(metrics.memory_percent)
        self.disk_card.update_value(metrics.disk_percent)

        # Calculer l'utilisation réseau (simplifié)
        network_usage = min(100, (metrics.network_sent + metrics.network_recv) / (1024 * 1024))
        self.network_card.update_value(network_usage, " MB/s")

        # Ajouter aux graphiques
        self.cpu_chart.add_data_point(metrics.cpu_percent)
        self.memory_chart.add_data_point(metrics.memory_percent)

        # Mettre à jour le statut global
        max_usage = max(metrics.cpu_percent, metrics.memory_percent, metrics.disk_percent)
        if max_usage > 80:
            self.status_label.setText("🔴 Système surchargé")
            self.status_label.setStyleSheet("color: #EF4444; font-weight: bold;")
        elif max_usage > 60:
            self.status_label.setText("🟡 Système occupé")
            self.status_label.setStyleSheet("color: #F59E0B; font-weight: bold;")
        else:
            self.status_label.setText("🟢 Système normal")
            self.status_label.setStyleSheet("color: #10B981; font-weight: bold;")

    def handle_alert(self, alert_type: str, message: str):
        """Gère une alerte système."""
        self.alerts_count += 1
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Ajouter au journal
        self.alerts_text.append(f"[{timestamp}] {alert_type}: {message}")

        # Afficher une notification
        InfoBar.warning(
            title=f"Alerte {alert_type}",
            content=message,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=5000,
            parent=self
        )

    def clear_alerts(self):
        """Efface le journal des alertes."""
        self.alerts_text.clear()
        self.alerts_count = 0

    def export_report(self):
        """Exporte un rapport de performance."""
        # Simuler l'export
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"performance_report_{timestamp}.json"

        InfoBar.success(
            title="Rapport exporté",
            content=f"Rapport sauvegardé: {filename}",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

    def open_settings(self):
        """Ouvre les paramètres de monitoring."""
        InfoBar.info(
            title="Paramètres",
            content="Configuration du monitoring (à implémenter)",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

    def closeEvent(self, event):
        """Appelé à la fermeture du widget."""
        self.monitor.stop_monitoring()
        super().closeEvent(event)
