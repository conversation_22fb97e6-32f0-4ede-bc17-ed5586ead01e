"""
Système de raccourcis clavier pour GSCOM.
Gestion centralisée des raccourcis avec interface de configuration.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QLabel, QPushButton,
    QDialog, QLineEdit, QComboBox, QMessageBox
)
from PyQt5.QtCore import Qt, QObject, pyqtSignal
from PyQt5.QtGui import QKeySequence, QFont

from qfluentwidgets import FluentIcon, IconWidget, PushButton, LineEdit, ComboBox
from ui.themes.modern_themes import modern_theme_manager
import json
import os


class ShortcutAction:
    """Action associée à un raccourci."""
    
    def __init__(self, name, description, category, default_shortcut, callback=None):
        self.name = name
        self.description = description
        self.category = category
        self.default_shortcut = default_shortcut
        self.current_shortcut = default_shortcut
        self.callback = callback
        self.enabled = True


class ShortcutManager(QObject):
    """Gestionnaire de raccourcis clavier."""
    
    shortcutTriggered = pyqtSignal(str)  # Nom de l'action
    
    def __init__(self):
        super().__init__()
        self.actions = {}
        self.shortcuts_file = "config/shortcuts.json"
        self.initialize_default_shortcuts()
        self.load_shortcuts()
        
    def initialize_default_shortcuts(self):
        """Initialise les raccourcis par défaut."""
        default_actions = [
            # Navigation
            ShortcutAction("dashboard", "Aller au tableau de bord", "Navigation", "Ctrl+H"),
            ShortcutAction("products", "Ouvrir les produits", "Navigation", "Ctrl+P"),
            ShortcutAction("customers", "Ouvrir les clients", "Navigation", "Ctrl+U"),
            ShortcutAction("suppliers", "Ouvrir les fournisseurs", "Navigation", "Ctrl+Shift+U"),
            ShortcutAction("sales", "Ouvrir les ventes", "Navigation", "Ctrl+V"),
            ShortcutAction("inventory", "Ouvrir l'inventaire", "Navigation", "Ctrl+I"),
            ShortcutAction("reports", "Ouvrir les rapports", "Navigation", "Ctrl+R"),
            ShortcutAction("settings", "Ouvrir les paramètres", "Navigation", "Ctrl+,"),
            
            # Actions
            ShortcutAction("new_sale", "Nouvelle vente", "Actions", "Ctrl+N"),
            ShortcutAction("new_customer", "Nouveau client", "Actions", "Ctrl+Shift+N"),
            ShortcutAction("search", "Recherche globale", "Actions", "Ctrl+F"),
            ShortcutAction("save", "Sauvegarder", "Actions", "Ctrl+S"),
            ShortcutAction("refresh", "Actualiser", "Actions", "F5"),
            ShortcutAction("print", "Imprimer", "Actions", "Ctrl+P"),
            ShortcutAction("export", "Exporter", "Actions", "Ctrl+E"),
            
            # Interface
            ShortcutAction("toggle_theme", "Changer de thème", "Interface", "Ctrl+T"),
            ShortcutAction("fullscreen", "Mode plein écran", "Interface", "F11"),
            ShortcutAction("zoom_in", "Zoom avant", "Interface", "Ctrl++"),
            ShortcutAction("zoom_out", "Zoom arrière", "Interface", "Ctrl+-"),
            ShortcutAction("zoom_reset", "Réinitialiser le zoom", "Interface", "Ctrl+0"),
            
            # Système
            ShortcutAction("help", "Aide", "Système", "F1"),
            ShortcutAction("about", "À propos", "Système", "Ctrl+Shift+A"),
            ShortcutAction("logout", "Déconnexion", "Système", "Ctrl+Shift+Q"),
            ShortcutAction("quit", "Quitter", "Système", "Ctrl+Q"),
        ]
        
        for action in default_actions:
            self.actions[action.name] = action
            
    def register_action(self, name, description, category, default_shortcut, callback=None):
        """Enregistre une nouvelle action."""
        action = ShortcutAction(name, description, category, default_shortcut, callback)
        self.actions[name] = action
        
    def set_shortcut(self, action_name, shortcut):
        """Définit un raccourci pour une action."""
        if action_name in self.actions:
            self.actions[action_name].current_shortcut = shortcut
            
    def get_shortcut(self, action_name):
        """Récupère le raccourci d'une action."""
        if action_name in self.actions:
            return self.actions[action_name].current_shortcut
        return None
        
    def trigger_action(self, action_name):
        """Déclenche une action."""
        if action_name in self.actions:
            action = self.actions[action_name]
            if action.enabled:
                if action.callback:
                    action.callback()
                self.shortcutTriggered.emit(action_name)
                
    def get_actions_by_category(self, category):
        """Récupère les actions par catégorie."""
        return [action for action in self.actions.values() if action.category == category]
        
    def get_all_categories(self):
        """Récupère toutes les catégories."""
        categories = set(action.category for action in self.actions.values())
        return sorted(list(categories))
        
    def save_shortcuts(self):
        """Sauvegarde les raccourcis dans un fichier."""
        try:
            # Créer le dossier config s'il n'existe pas
            os.makedirs(os.path.dirname(self.shortcuts_file), exist_ok=True)
            
            shortcuts_data = {}
            for name, action in self.actions.items():
                shortcuts_data[name] = {
                    "current_shortcut": action.current_shortcut,
                    "enabled": action.enabled
                }
                
            with open(self.shortcuts_file, 'w', encoding='utf-8') as f:
                json.dump(shortcuts_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des raccourcis: {e}")
            
    def load_shortcuts(self):
        """Charge les raccourcis depuis un fichier."""
        try:
            if os.path.exists(self.shortcuts_file):
                with open(self.shortcuts_file, 'r', encoding='utf-8') as f:
                    shortcuts_data = json.load(f)
                    
                for name, data in shortcuts_data.items():
                    if name in self.actions:
                        self.actions[name].current_shortcut = data.get("current_shortcut", self.actions[name].default_shortcut)
                        self.actions[name].enabled = data.get("enabled", True)
                        
        except Exception as e:
            print(f"Erreur lors du chargement des raccourcis: {e}")
            
    def reset_to_defaults(self):
        """Remet les raccourcis par défaut."""
        for action in self.actions.values():
            action.current_shortcut = action.default_shortcut
            action.enabled = True


class ShortcutEditDialog(QDialog):
    """Dialog pour éditer un raccourci."""
    
    def __init__(self, action: ShortcutAction, parent=None):
        super().__init__(parent)
        self.action = action
        self.new_shortcut = action.current_shortcut
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface du dialog."""
        self.setWindowTitle(f"Modifier le raccourci - {self.action.description}")
        self.setFixedSize(400, 200)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Informations de l'action
        info_layout = QVBoxLayout()
        
        name_label = QLabel(f"Action: {self.action.description}")
        name_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        category_label = QLabel(f"Catégorie: {self.action.category}")
        category_label.setStyleSheet("color: #6B7280;")
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(category_label)
        
        # Champ de raccourci
        shortcut_layout = QVBoxLayout()
        
        shortcut_label = QLabel("Nouveau raccourci:")
        self.shortcut_edit = LineEdit()
        self.shortcut_edit.setText(self.action.current_shortcut)
        self.shortcut_edit.setPlaceholderText("Appuyez sur les touches...")
        
        shortcut_layout.addWidget(shortcut_label)
        shortcut_layout.addWidget(self.shortcut_edit)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        reset_btn = QPushButton("Par défaut")
        reset_btn.clicked.connect(self.reset_to_default)
        
        cancel_btn = QPushButton("Annuler")
        cancel_btn.clicked.connect(self.reject)
        
        ok_btn = QPushButton("OK")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        
        buttons_layout.addWidget(reset_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(ok_btn)
        
        layout.addLayout(info_layout)
        layout.addLayout(shortcut_layout)
        layout.addLayout(buttons_layout)
        
        # Style
        colors = modern_theme_manager.get_colors()
        self.setStyleSheet(f"""
            QDialog {{
                background: {colors['background']};
            }}
            QLabel {{
                color: {colors['text']};
            }}
        """)
        
    def reset_to_default(self):
        """Remet le raccourci par défaut."""
        self.shortcut_edit.setText(self.action.default_shortcut)
        
    def get_shortcut(self):
        """Récupère le nouveau raccourci."""
        return self.shortcut_edit.text().strip()


class ShortcutsConfigWidget(QWidget):
    """Widget de configuration des raccourcis."""
    
    def __init__(self, shortcut_manager: ShortcutManager, parent=None):
        super().__init__(parent)
        self.shortcut_manager = shortcut_manager
        self.setupUI()
        self.load_shortcuts()
        
    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Configuration des Raccourcis Clavier")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        
        # Filtre par catégorie
        filter_layout = QHBoxLayout()
        filter_label = QLabel("Catégorie:")
        self.category_filter = ComboBox()
        self.category_filter.addItem("Toutes")
        self.category_filter.addItems(self.shortcut_manager.get_all_categories())
        self.category_filter.currentTextChanged.connect(self.filter_shortcuts)
        
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.category_filter)
        filter_layout.addStretch()
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addLayout(filter_layout)
        
        # Tableau des raccourcis
        self.shortcuts_table = QTableWidget()
        self.shortcuts_table.setColumnCount(4)
        self.shortcuts_table.setHorizontalHeaderLabels([
            "Description", "Catégorie", "Raccourci", "Actions"
        ])
        
        # Configuration du tableau
        header = self.shortcuts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        self.shortcuts_table.setAlternatingRowColors(True)
        self.shortcuts_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        reset_all_btn = PushButton("Tout réinitialiser")
        reset_all_btn.setIcon(FluentIcon.CANCEL.icon())
        reset_all_btn.clicked.connect(self.reset_all_shortcuts)
        
        save_btn = PushButton("Sauvegarder")
        save_btn.setIcon(FluentIcon.SAVE.icon())
        save_btn.clicked.connect(self.save_shortcuts)
        
        buttons_layout.addWidget(reset_all_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.shortcuts_table)
        layout.addLayout(buttons_layout)
        
        # Style
        self.update_style()
        
    def load_shortcuts(self):
        """Charge les raccourcis dans le tableau."""
        actions = list(self.shortcut_manager.actions.values())
        actions.sort(key=lambda x: (x.category, x.description))
        
        self.shortcuts_table.setRowCount(len(actions))
        
        for row, action in enumerate(actions):
            # Description
            desc_item = QTableWidgetItem(action.description)
            desc_item.setFlags(desc_item.flags() & ~Qt.ItemIsEditable)
            self.shortcuts_table.setItem(row, 0, desc_item)
            
            # Catégorie
            cat_item = QTableWidgetItem(action.category)
            cat_item.setFlags(cat_item.flags() & ~Qt.ItemIsEditable)
            self.shortcuts_table.setItem(row, 1, cat_item)
            
            # Raccourci
            shortcut_item = QTableWidgetItem(action.current_shortcut)
            shortcut_item.setFlags(shortcut_item.flags() & ~Qt.ItemIsEditable)
            self.shortcuts_table.setItem(row, 2, shortcut_item)
            
            # Bouton d'édition
            edit_btn = QPushButton("Modifier")
            edit_btn.clicked.connect(lambda checked, a=action, r=row: self.edit_shortcut(a, r))
            self.shortcuts_table.setCellWidget(row, 3, edit_btn)
            
    def filter_shortcuts(self, category):
        """Filtre les raccourcis par catégorie."""
        for row in range(self.shortcuts_table.rowCount()):
            cat_item = self.shortcuts_table.item(row, 1)
            if category == "Toutes" or cat_item.text() == category:
                self.shortcuts_table.setRowHidden(row, False)
            else:
                self.shortcuts_table.setRowHidden(row, True)
                
    def edit_shortcut(self, action, row):
        """Édite un raccourci."""
        dialog = ShortcutEditDialog(action, self)
        if dialog.exec_() == QDialog.Accepted:
            new_shortcut = dialog.get_shortcut()
            if new_shortcut:
                # Vérifier les conflits
                if self.check_shortcut_conflict(new_shortcut, action.name):
                    QMessageBox.warning(
                        self,
                        "Conflit de raccourci",
                        f"Le raccourci '{new_shortcut}' est déjà utilisé par une autre action."
                    )
                    return
                    
                # Mettre à jour le raccourci
                action.current_shortcut = new_shortcut
                self.shortcuts_table.item(row, 2).setText(new_shortcut)
                
    def check_shortcut_conflict(self, shortcut, exclude_action):
        """Vérifie s'il y a un conflit de raccourci."""
        for name, action in self.shortcut_manager.actions.items():
            if name != exclude_action and action.current_shortcut == shortcut:
                return True
        return False
        
    def reset_all_shortcuts(self):
        """Remet tous les raccourcis par défaut."""
        reply = QMessageBox.question(
            self,
            "Réinitialiser les raccourcis",
            "Êtes-vous sûr de vouloir remettre tous les raccourcis par défaut ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.shortcut_manager.reset_to_defaults()
            self.load_shortcuts()
            
    def save_shortcuts(self):
        """Sauvegarde les raccourcis."""
        self.shortcut_manager.save_shortcuts()
        QMessageBox.information(
            self,
            "Raccourcis sauvegardés",
            "Les raccourcis ont été sauvegardés avec succès."
        )
        
    def update_style(self):
        """Met à jour le style."""
        colors = modern_theme_manager.get_colors()
        
        self.setStyleSheet(f"""
            QWidget {{
                background: {colors['background']};
                color: {colors['text']};
            }}
            QTableWidget {{
                background: {colors['surface']};
                alternate-background-color: {colors['primary']}10;
                gridline-color: {colors['border']};
                border: 1px solid {colors['border']};
                border-radius: 8px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border: none;
            }}
            QHeaderView::section {{
                background: {colors['primary']}20;
                color: {colors['text']};
                border: none;
                padding: 8px;
                font-weight: bold;
            }}
        """)


# Instance globale du gestionnaire de raccourcis
shortcut_manager = ShortcutManager()
