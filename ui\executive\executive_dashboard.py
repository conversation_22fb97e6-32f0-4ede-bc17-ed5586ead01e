"""
Tableau de bord exécutif pour GSCOM.
Vue d'ensemble stratégique avec KPIs et métriques business.
"""
import random
import math
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QTabWidget, QScrollArea,
    QSplitter, QCalendarWidget, QComboBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPainter, QColor, QLinearGradient, QBrush, QPen

from qfluentwidgets import FluentIcon, IconWidget, PushButton, CardWidget, ComboBox
from ui.themes.modern_themes import modern_theme_manager
from ui.components.charts import <PERSON><PERSON><PERSON>, DonutChart, BarChart
import json


class KPICard(CardWidget):
    """Carte KPI avec tendance et objectif."""
    
    def __init__(self, title, current_value, target_value, unit="", trend=0.0, parent=None):
        super().__init__(parent)
        self.title = title
        self.current_value = current_value
        self.target_value = target_value
        self.unit = unit
        self.trend = trend
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface de la carte."""
        self.setFixedSize(280, 160)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setObjectName("kpiTitle")
        title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        # Valeur actuelle
        value_layout = QHBoxLayout()
        
        current_label = QLabel(f"{self.current_value:,.0f}{self.unit}")
        current_label.setObjectName("kpiValue")
        current_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        
        # Indicateur de tendance
        trend_icon = "📈" if self.trend > 0 else "📉" if self.trend < 0 else "➡️"
        trend_color = "#10B981" if self.trend > 0 else "#EF4444" if self.trend < 0 else "#6B7280"
        
        trend_label = QLabel(f"{trend_icon} {abs(self.trend):.1f}%")
        trend_label.setStyleSheet(f"color: {trend_color}; font-size: 12px; font-weight: bold;")
        
        value_layout.addWidget(current_label)
        value_layout.addStretch()
        value_layout.addWidget(trend_label)
        
        # Objectif et progression
        target_layout = QHBoxLayout()
        
        target_label = QLabel(f"Objectif: {self.target_value:,.0f}{self.unit}")
        target_label.setObjectName("kpiTarget")
        target_label.setFont(QFont("Segoe UI", 10))
        
        # Calcul du pourcentage d'atteinte
        achievement = (self.current_value / self.target_value * 100) if self.target_value > 0 else 0
        achievement_label = QLabel(f"{achievement:.0f}%")
        achievement_label.setObjectName("kpiAchievement")
        achievement_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        
        # Couleur selon l'atteinte
        if achievement >= 100:
            achievement_color = "#10B981"
        elif achievement >= 80:
            achievement_color = "#F59E0B"
        else:
            achievement_color = "#EF4444"
            
        achievement_label.setStyleSheet(f"color: {achievement_color};")
        
        target_layout.addWidget(target_label)
        target_layout.addStretch()
        target_layout.addWidget(achievement_label)
        
        # Barre de progression
        progress_frame = QFrame()
        progress_frame.setFixedHeight(8)
        progress_frame.setStyleSheet(f"""
            QFrame {{
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }}
        """)
        
        # Dessiner la progression
        self.progress_value = min(100, achievement)
        
        layout.addWidget(title_label)
        layout.addLayout(value_layout)
        layout.addLayout(target_layout)
        layout.addWidget(progress_frame)
        
        # Style
        self.apply_style()
        
    def apply_style(self):
        """Applique le style à la carte."""
        colors = modern_theme_manager.get_colors()
        
        # Couleur de bordure selon la performance
        if self.progress_value >= 100:
            border_color = "#10B981"
        elif self.progress_value >= 80:
            border_color = "#F59E0B"
        else:
            border_color = "#EF4444"
            
        self.setStyleSheet(f"""
            KPICard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {border_color}15,
                    stop:1 {border_color}08);
                border: 1px solid {border_color}40;
                border-left: 4px solid {border_color};
                border-radius: 12px;
            }}
            QLabel#kpiTitle {{
                color: {colors['text_secondary']};
            }}
            QLabel#kpiValue {{
                color: {border_color};
            }}
            QLabel#kpiTarget {{
                color: {colors['text_secondary']};
            }}
        """)
        
    def paintEvent(self, event):
        """Dessine la barre de progression personnalisée."""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Position de la barre de progression
        rect = self.rect()
        progress_rect = rect.adjusted(20, rect.height() - 25, -20, -15)
        
        # Fond de la barre
        painter.setBrush(QBrush(QColor(255, 255, 255, 25)))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(progress_rect, 4, 4)
        
        # Barre de progression
        progress_width = int(progress_rect.width() * self.progress_value / 100)
        if progress_width > 0:
            progress_fill_rect = progress_rect.adjusted(0, 0, progress_width - progress_rect.width(), 0)
            
            # Gradient pour la progression
            gradient = QLinearGradient(progress_fill_rect.topLeft(), progress_fill_rect.topRight())
            if self.progress_value >= 100:
                gradient.setColorAt(0, QColor("#10B981"))
                gradient.setColorAt(1, QColor("#059669"))
            elif self.progress_value >= 80:
                gradient.setColorAt(0, QColor("#F59E0B"))
                gradient.setColorAt(1, QColor("#D97706"))
            else:
                gradient.setColorAt(0, QColor("#EF4444"))
                gradient.setColorAt(1, QColor("#DC2626"))
                
            painter.setBrush(QBrush(gradient))
            painter.drawRoundedRect(progress_fill_rect, 4, 4)


class BusinessMetricsWidget(QWidget):
    """Widget des métriques business."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Titre
        title_label = QLabel("Métriques Business")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        
        # Grille de KPIs
        kpis_layout = QGridLayout()
        kpis_layout.setSpacing(15)
        
        # KPIs simulés
        kpis = [
            ("Chiffre d'Affaires", 1250000, 1500000, " DA", 8.5),
            ("Marge Brute", 425000, 500000, " DA", 12.3),
            ("Nouveaux Clients", 47, 60, "", 15.2),
            ("Taux de Conversion", 3.2, 5.0, "%", -2.1),
            ("Panier Moyen", 2850, 3000, " DA", 5.7),
            ("Satisfaction Client", 4.3, 4.5, "/5", 2.4)
        ]
        
        for i, (title, current, target, unit, trend) in enumerate(kpis):
            kpi_card = KPICard(title, current, target, unit, trend)
            kpis_layout.addWidget(kpi_card, i // 3, i % 3)
            
        layout.addWidget(title_label)
        layout.addLayout(kpis_layout)


class FinancialSummaryWidget(QWidget):
    """Widget de résumé financier."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Titre
        title_label = QLabel("Résumé Financier")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        
        # Layout horizontal pour les graphiques
        charts_layout = QHBoxLayout()
        
        # Graphique de répartition des revenus
        self.revenue_chart = DonutChart("Répartition des Revenus")
        self.revenue_chart.segments = [
            {"label": "Ventes directes", "value": 65, "color": "#3B82F6"},
            {"label": "E-commerce", "value": 25, "color": "#10B981"},
            {"label": "Partenaires", "value": 10, "color": "#F59E0B"}
        ]
        
        # Graphique des dépenses
        self.expenses_chart = DonutChart("Répartition des Dépenses")
        self.expenses_chart.segments = [
            {"label": "Personnel", "value": 45, "color": "#EF4444"},
            {"label": "Marchandises", "value": 35, "color": "#8B5CF6"},
            {"label": "Opérationnel", "value": 20, "color": "#6B7280"}
        ]
        
        charts_layout.addWidget(self.revenue_chart)
        charts_layout.addWidget(self.expenses_chart)
        
        # Métriques financières rapides
        metrics_layout = QHBoxLayout()
        
        metrics = [
            ("Trésorerie", "485 670 DA", "#10B981"),
            ("Créances", "125 340 DA", "#F59E0B"),
            ("Dettes", "89 250 DA", "#EF4444"),
            ("Résultat Net", "+156 780 DA", "#3B82F6")
        ]
        
        for title, value, color in metrics:
            metric_frame = QFrame()
            metric_frame.setFixedSize(150, 80)
            
            metric_layout = QVBoxLayout(metric_frame)
            metric_layout.setContentsMargins(10, 10, 10, 10)
            
            title_label = QLabel(title)
            title_label.setFont(QFont("Segoe UI", 10))
            title_label.setAlignment(Qt.AlignCenter)
            
            value_label = QLabel(value)
            value_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
            value_label.setAlignment(Qt.AlignCenter)
            value_label.setStyleSheet(f"color: {color};")
            
            metric_layout.addWidget(title_label)
            metric_layout.addWidget(value_label)
            
            # Style du frame
            colors = modern_theme_manager.get_colors()
            metric_frame.setStyleSheet(f"""
                QFrame {{
                    background: {colors['surface']};
                    border: 1px solid {color}40;
                    border-radius: 8px;
                }}
                QLabel {{
                    color: {colors['text']};
                    background: transparent;
                }}
            """)
            
            metrics_layout.addWidget(metric_frame)
            
        metrics_layout.addStretch()
        
        layout.addWidget(title_label)
        layout.addLayout(charts_layout)
        layout.addLayout(metrics_layout)


class ExecutiveDashboard(QWidget):
    """Tableau de bord exécutif principal."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        self.setupTimers()
        self.loadData()
        
    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête exécutif
        header_layout = self.createExecutiveHeader()
        layout.addLayout(header_layout)
        
        # Splitter principal
        main_splitter = QSplitter(Qt.Vertical)
        
        # Section supérieure - KPIs et métriques
        top_section = self.createTopSection()
        main_splitter.addWidget(top_section)
        
        # Section inférieure - Graphiques et analyses
        bottom_section = self.createBottomSection()
        main_splitter.addWidget(bottom_section)
        
        # Proportions
        main_splitter.setSizes([400, 300])
        
        layout.addWidget(main_splitter)
        
    def createExecutiveHeader(self):
        """Crée l'en-tête exécutif."""
        layout = QHBoxLayout()
        
        # Informations principales
        info_layout = QVBoxLayout()
        
        title_label = QLabel("Tableau de Bord Exécutif")
        title_label.setFont(QFont("Segoe UI", 28, QFont.Bold))
        
        date_label = QLabel(datetime.now().strftime("%A, %d %B %Y"))
        date_label.setFont(QFont("Segoe UI", 14))
        date_label.setStyleSheet("color: #6B7280;")
        
        # Indicateur de performance globale
        performance_label = QLabel("Performance Globale: 📈 +12.5%")
        performance_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        performance_label.setStyleSheet("color: #10B981;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(date_label)
        info_layout.addWidget(performance_label)
        
        # Contrôles de période
        controls_layout = QVBoxLayout()
        
        period_label = QLabel("Période:")
        self.period_combo = ComboBox()
        self.period_combo.addItems([
            "Aujourd'hui", "Cette semaine", "Ce mois", 
            "Ce trimestre", "Cette année", "Personnalisé"
        ])
        self.period_combo.setCurrentText("Ce mois")
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        
        # Boutons d'action
        export_btn = PushButton("Exporter Rapport")
        export_btn.setIcon(FluentIcon.DOWNLOAD.icon())
        export_btn.clicked.connect(self.export_executive_report)
        
        meeting_btn = PushButton("Préparer Réunion")
        meeting_btn.setIcon(FluentIcon.CALENDAR.icon())
        meeting_btn.clicked.connect(self.prepare_meeting)
        
        controls_layout.addWidget(period_label)
        controls_layout.addWidget(self.period_combo)
        controls_layout.addWidget(export_btn)
        controls_layout.addWidget(meeting_btn)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        layout.addLayout(controls_layout)
        
        return layout
        
    def createTopSection(self):
        """Crée la section supérieure."""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setSpacing(20)
        
        # Métriques business
        self.business_metrics = BusinessMetricsWidget()
        
        # Résumé financier
        self.financial_summary = FinancialSummaryWidget()
        
        layout.addWidget(self.business_metrics, 2)
        layout.addWidget(self.financial_summary, 1)
        
        return widget
        
    def createBottomSection(self):
        """Crée la section inférieure."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Onglets d'analyse
        tabs = QTabWidget()
        
        # Onglet Tendances
        trends_tab = self.createTrendsTab()
        tabs.addTab(trends_tab, "📈 Tendances")
        
        # Onglet Comparaisons
        comparison_tab = self.createComparisonTab()
        tabs.addTab(comparison_tab, "⚖️ Comparaisons")
        
        # Onglet Prévisions
        forecast_tab = self.createForecastTab()
        tabs.addTab(forecast_tab, "🔮 Prévisions")
        
        layout.addWidget(tabs)
        
        return widget
        
    def createTrendsTab(self):
        """Crée l'onglet des tendances."""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Graphique des ventes
        sales_chart = BarChart("Évolution des Ventes (6 mois)")
        sales_chart.bars = [
            {"label": "Jan", "value": 980000},
            {"label": "Fév", "value": 1050000},
            {"label": "Mar", "value": 1120000},
            {"label": "Avr", "value": 1180000},
            {"label": "Mai", "value": 1250000},
            {"label": "Jun", "value": 1320000}
        ]
        
        # Graphique des clients
        customers_chart = AnimatedChart("Acquisition de Clients")
        # Ajouter des données simulées
        for i in range(30):
            value = 40 + random.randint(-5, 10) + math.sin(i * 0.2) * 5
            customers_chart.add_data_point(value)
            
        layout.addWidget(sales_chart)
        layout.addWidget(customers_chart)
        
        return widget
        
    def createComparisonTab(self):
        """Crée l'onglet des comparaisons."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        comparison_label = QLabel("Comparaisons Période vs Période")
        comparison_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        comparison_label.setAlignment(Qt.AlignCenter)
        
        # Tableau de comparaison (simplifié)
        comparison_text = QLabel("""
        📊 Comparaison avec la période précédente:
        
        • Chiffre d'affaires: +12.5% (vs mois dernier)
        • Nouveaux clients: +15.2% (vs mois dernier)
        • Marge brute: +8.7% (vs mois dernier)
        • Satisfaction: +2.4% (vs mois dernier)
        
        🎯 Comparaison avec l'objectif annuel:
        
        • CA réalisé: 83.3% de l'objectif
        • Clients acquis: 78.3% de l'objectif
        • Marge atteinte: 85.0% de l'objectif
        """)
        comparison_text.setStyleSheet("font-size: 14px; line-height: 1.6;")
        
        layout.addWidget(comparison_label)
        layout.addWidget(comparison_text)
        layout.addStretch()
        
        return widget
        
    def createForecastTab(self):
        """Crée l'onglet des prévisions."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        forecast_label = QLabel("Prévisions et Projections")
        forecast_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        forecast_label.setAlignment(Qt.AlignCenter)
        
        # Prévisions (simulées)
        forecast_text = QLabel("""
        🔮 Prévisions pour les 3 prochains mois:
        
        • Juillet: CA prévu 1,380,000 DA (+4.5%)
        • Août: CA prévu 1,420,000 DA (+2.9%)
        • Septembre: CA prévu 1,465,000 DA (+3.2%)
        
        📈 Tendances identifiées:
        
        • Croissance soutenue du e-commerce (+25% YoY)
        • Augmentation du panier moyen (+8% YoY)
        • Amélioration de la fidélisation (+12% YoY)
        
        ⚠️ Points d'attention:
        
        • Saisonnalité estivale à anticiper
        • Concurrence accrue sur certains segments
        • Optimisation des coûts logistiques
        """)
        forecast_text.setStyleSheet("font-size: 14px; line-height: 1.6;")
        
        layout.addWidget(forecast_label)
        layout.addWidget(forecast_text)
        layout.addStretch()
        
        return widget
        
    def setupTimers(self):
        """Configure les timers de mise à jour."""
        # Timer pour mise à jour des données
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # Toutes les minutes
        
    def loadData(self):
        """Charge les données initiales."""
        # Démarrer les animations des graphiques
        QTimer.singleShot(500, lambda: self.financial_summary.revenue_chart.start_animation())
        QTimer.singleShot(700, lambda: self.financial_summary.expenses_chart.start_animation())
        
    def refresh_data(self):
        """Actualise les données."""
        # Simuler la mise à jour des données
        pass
        
    def on_period_changed(self, period):
        """Appelé quand la période change."""
        from ui.components.modern_notifications import show_info
        show_info("Période changée", f"Affichage des données pour: {period}")
        
    def export_executive_report(self):
        """Exporte un rapport exécutif."""
        from ui.components.modern_notifications import show_success
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"rapport_executif_{timestamp}.pdf"
        show_success("Rapport exporté", f"Rapport exécutif sauvegardé: {filename}")
        
    def prepare_meeting(self):
        """Prépare les données pour une réunion."""
        from ui.components.modern_notifications import show_info
        show_info("Préparation réunion", "Génération du support de présentation...")
