"""
Système de notifications pour l'application SGest.
"""
from PyQt5.QtWidgets import <PERSON>Widget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPainter, QPen, QBrush, QColor
from qfluentwidgets import FluentIcon, IconWidget
from datetime import datetime
from enum import Enum


class NotificationType(Enum):
    """Types de notifications."""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"


class NotificationWidget(QFrame):
    """Widget de notification individuelle."""

    # Signaux
    closeRequested = pyqtSignal()

    def __init__(self, title, message, notification_type=NotificationType.INFO, duration=5000, parent=None):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        self.setupUI()
        self.setupTimer()
        self.setupAnimation()

    def setupUI(self):
        """Configure l'interface de la notification."""
        self.setFixedHeight(80)
        self.setMinimumWidth(350)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)

        # Icône selon le type
        icon_map = {
            NotificationType.INFO: FluentIcon.INFO,
            NotificationType.SUCCESS: FluentIcon.ACCEPT,
            NotificationType.WARNING: FluentIcon.FEEDBACK,  # Utiliser FEEDBACK pour les avertissements
            NotificationType.ERROR: FluentIcon.CANCEL
        }

        color_map = {
            NotificationType.INFO: "#38BDF8",
            NotificationType.SUCCESS: "#10B981",
            NotificationType.WARNING: "#F59E0B",
            NotificationType.ERROR: "#EF4444"
        }

        # Icône
        self.icon_widget = IconWidget(icon_map[self.notification_type], self)
        self.icon_widget.setFixedSize(24, 24)

        # Contenu
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)

        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {color_map[self.notification_type]};
                font-size: 14px;
                font-weight: bold;
            }}
        """)

        # Message
        message_label = QLabel(self.message)
        message_label.setStyleSheet("""
            QLabel {
                color: #F1F5F9;
                font-size: 12px;
            }
        """)
        message_label.setWordWrap(True)

        content_layout.addWidget(title_label)
        content_layout.addWidget(message_label)

        # Bouton de fermeture
        close_btn = QPushButton("×")
        close_btn.setFixedSize(20, 20)
        close_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #94A3B8;
                border: none;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #334155;
                border-radius: 10px;
            }
        """)
        close_btn.clicked.connect(self.closeRequested.emit)

        layout.addWidget(self.icon_widget)
        layout.addLayout(content_layout)
        layout.addStretch()
        layout.addWidget(close_btn)

        # Style de la notification
        border_color = color_map[self.notification_type]
        self.setStyleSheet(f"""
            NotificationWidget {{
                background-color: #23263A;
                border: 1px solid {border_color};
                border-left: 4px solid {border_color};
                border-radius: 8px;
            }}
        """)

    def setupTimer(self):
        """Configure le timer pour la fermeture automatique."""
        if self.duration > 0:
            self.timer = QTimer(self)
            self.timer.timeout.connect(self.closeRequested.emit)
            self.timer.start(self.duration)

    def setupAnimation(self):
        """Configure l'animation d'apparition."""
        self.setWindowOpacity(0)

        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0)
        self.fade_animation.setEndValue(1)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.fade_animation.start()

    def closeWithAnimation(self):
        """Ferme la notification avec une animation."""
        self.fade_out_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_out_animation.setDuration(200)
        self.fade_out_animation.setStartValue(1)
        self.fade_out_animation.setEndValue(0)
        self.fade_out_animation.setEasingCurve(QEasingCurve.InCubic)
        self.fade_out_animation.finished.connect(self.deleteLater)
        self.fade_out_animation.start()


class NotificationManager(QWidget):
    """Gestionnaire de notifications."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.notifications = []
        self.setupUI()

    def setupUI(self):
        """Configure l'interface du gestionnaire."""
        self.setFixedWidth(400)
        self.setAttribute(Qt.WA_TransparentForMouseEvents, False)

        # Layout vertical pour empiler les notifications
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(10)
        self.layout.addStretch()  # Pousser les notifications vers le bas

        # Positionner en haut à droite de la fenêtre parent
        if self.parent():
            self.move(
                self.parent().width() - self.width() - 20,
                20
            )

        # Style transparent
        self.setStyleSheet("""
            NotificationManager {
                background: transparent;
            }
        """)

    def addNotification(self, title, message, notification_type=NotificationType.INFO, duration=5000):
        """Ajoute une nouvelle notification."""
        notification = NotificationWidget(title, message, notification_type, duration, self)
        notification.closeRequested.connect(lambda: self.removeNotification(notification))

        # Ajouter à la liste et au layout
        self.notifications.append(notification)
        self.layout.insertWidget(self.layout.count() - 1, notification)  # Avant le stretch

        # Limiter le nombre de notifications affichées
        if len(self.notifications) > 5:
            oldest = self.notifications.pop(0)
            oldest.closeWithAnimation()

        # Repositionner si nécessaire
        self.adjustPosition()

    def removeNotification(self, notification):
        """Supprime une notification."""
        if notification in self.notifications:
            self.notifications.remove(notification)
            notification.closeWithAnimation()

        # Repositionner les notifications restantes
        self.adjustPosition()

    def adjustPosition(self):
        """Ajuste la position du gestionnaire selon le parent."""
        if self.parent():
            self.move(
                self.parent().width() - self.width() - 20,
                20
            )

    def showInfo(self, title, message, duration=5000):
        """Affiche une notification d'information."""
        self.addNotification(title, message, NotificationType.INFO, duration)

    def showSuccess(self, title, message, duration=3000):
        """Affiche une notification de succès."""
        self.addNotification(title, message, NotificationType.SUCCESS, duration)

    def showWarning(self, title, message, duration=7000):
        """Affiche une notification d'avertissement."""
        self.addNotification(title, message, NotificationType.WARNING, duration)

    def showError(self, title, message, duration=10000):
        """Affiche une notification d'erreur."""
        self.addNotification(title, message, NotificationType.ERROR, duration)

    def clearAll(self):
        """Supprime toutes les notifications."""
        for notification in self.notifications[:]:
            self.removeNotification(notification)


# Instance globale du gestionnaire de notifications
_notification_manager = None


def get_notification_manager(parent=None):
    """Retourne l'instance globale du gestionnaire de notifications."""
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = NotificationManager(parent)
    return _notification_manager


def show_info(title, message, duration=5000, parent=None):
    """Affiche une notification d'information."""
    manager = get_notification_manager(parent)
    manager.showInfo(title, message, duration)


def show_success(title, message, duration=3000, parent=None):
    """Affiche une notification de succès."""
    manager = get_notification_manager(parent)
    manager.showSuccess(title, message, duration)


def show_warning(title, message, duration=7000, parent=None):
    """Affiche une notification d'avertissement."""
    manager = get_notification_manager(parent)
    manager.showWarning(title, message, duration)


def show_error(title, message, duration=10000, parent=None):
    """Affiche une notification d'erreur."""
    manager = get_notification_manager(parent)
    manager.showError(title, message, duration)


def clear_all_notifications():
    """Supprime toutes les notifications."""
    global _notification_manager
    if _notification_manager:
        _notification_manager.clearAll()


# Classe pour les notifications système
class SystemNotifications:
    """Notifications système prédéfinies."""

    @staticmethod
    def stock_low(product_name, current_stock, min_stock, parent=None):
        """Notification de stock faible."""
        show_warning(
            "Stock faible",
            f"Le produit '{product_name}' a un stock faible ({current_stock}/{min_stock})",
            parent=parent
        )

    @staticmethod
    def sale_completed(sale_number, amount, parent=None):
        """Notification de vente terminée."""
        show_success(
            "Vente terminée",
            f"Vente {sale_number} terminée avec succès ({amount})",
            parent=parent
        )

    @staticmethod
    def product_added(product_name, parent=None):
        """Notification de produit ajouté."""
        show_success(
            "Produit ajouté",
            f"Le produit '{product_name}' a été ajouté avec succès",
            parent=parent
        )

    @staticmethod
    def backup_completed(filename, parent=None):
        """Notification de sauvegarde terminée."""
        show_success(
            "Sauvegarde terminée",
            f"Sauvegarde créée: {filename}",
            parent=parent
        )

    @staticmethod
    def export_completed(filename, parent=None):
        """Notification d'export terminé."""
        show_success(
            "Export terminé",
            f"Données exportées vers: {filename}",
            parent=parent
        )

    @staticmethod
    def connection_error(error_message, parent=None):
        """Notification d'erreur de connexion."""
        show_error(
            "Erreur de connexion",
            f"Impossible de se connecter: {error_message}",
            parent=parent
        )

    @staticmethod
    def data_sync_completed(parent=None):
        """Notification de synchronisation terminée."""
        show_info(
            "Synchronisation",
            "Les données ont été synchronisées avec succès",
            parent=parent
        )
