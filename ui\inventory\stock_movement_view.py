"""
Vue des mouvements de stock pour l'application.
"""
from PyQt5.QtWidgets import (
    QHBoxLayout,
    QVBoxLayout,
    QWidget,
    QDialog,
    QMessageBox,
    QDateEdit,
    QLabel,
    QFrame
)
from PyQt5.QtCore import Qt, pyqtSlot, QDate
from PyQt5.QtGui import QIcon

from qfluentwidgets import (
    FluentIcon, InfoBar, InfoBarPosition,
    ComboBox, PushButton, LineEdit, SearchLineEdit, IconWidget
)

from ..components import BaseTable
from .stock_movement_form import StockMovementForm

class StockMovementView(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUI()
        self.loadData()
        
        print("INFO: StockMovementView initialisé.")
        
    def setupUI(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Ajout d'un bandeau supérieur moderne
        topBar = QHBoxLayout()
        titleIcon = IconWidget(FluentIcon.LIBRARY, self)
        titleIcon.setFixedSize(32, 32)
        titleLabel = QLabel("Mouvements de stock", self)
        titleLabel.setStyleSheet("font-size: 22px; font-weight: bold; margin-left: 10px;")
        topBar.addWidget(titleIcon)
        topBar.addWidget(titleLabel)
        topBar.addStretch()
        layout.addLayout(topBar)
        
        # Ajout d'une ombre sous le bandeau supérieur
        shadowFrame = QFrame(self)
        shadowFrame.setFixedHeight(2)
        shadowFrame.setStyleSheet("background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #38BDF8, stop:1 #0ea5e9); border-radius: 1px;")
        layout.addWidget(shadowFrame)
        
        # Filtres
        filterLayout = QHBoxLayout()
        
        # Filtre par période
        self.periodCombo = ComboBox(self)
        self.periodCombo.addItems(["Aujourd'hui", "Cette semaine", "Ce mois", "Cette année", "Personnalisé"])
        self.periodCombo.setCurrentIndex(2)  # Par défaut: Ce mois
        self.periodCombo.currentIndexChanged.connect(self.onPeriodChanged)
        
        # Dates personnalisées (cachées par défaut)
        self.startDateEdit = QDateEdit(self)
        self.startDateEdit.setDate(QDate.currentDate().addMonths(-1))
        self.startDateEdit.setCalendarPopup(True)
        self.startDateEdit.setVisible(False)
        
        self.endDateEdit = QDateEdit(self)
        self.endDateEdit.setDate(QDate.currentDate())
        self.endDateEdit.setCalendarPopup(True)
        self.endDateEdit.setVisible(False)
        
        # Filtre par type de mouvement
        self.typeCombo = ComboBox(self)
        self.typeCombo.addItems(["Tous", "Entrée", "Sortie", "Ajustement"])
        self.typeCombo.currentIndexChanged.connect(self.applyFilters)
        
        # Filtre par produit
        self.productCombo = ComboBox(self)
        self.productCombo.addItems(["Tous", "Ordinateur portable", "Imprimante laser", "Souris sans fil", "Clavier mécanique", "Écran 24 pouces"])
        self.productCombo.currentIndexChanged.connect(self.applyFilters)
        
        # Bouton d'application des filtres
        self.applyFilterButton = PushButton("Appliquer", self)
        self.applyFilterButton.clicked.connect(self.applyFilters)
        
        # Ajouter les widgets au layout des filtres
        filterLayout.addWidget(self.periodCombo)
        filterLayout.addWidget(self.startDateEdit)
        filterLayout.addWidget(self.endDateEdit)
        filterLayout.addWidget(self.typeCombo)
        filterLayout.addWidget(self.productCombo)
        filterLayout.addWidget(self.applyFilterButton)
        filterLayout.addStretch()
        
        # Créer le tableau des mouvements de stock
        self.movementTable = BaseTable(
            title="Mouvements de Stock",
            headers=["Référence", "Date", "Type", "Produit", "Quantité", "Emplacement", "Utilisateur"],
            parent=self
        )
        
        # Connecter les signaux
        self.movementTable.addButton.clicked.connect(self.openMovementForm)
        self.movementTable.rowDoubleClicked.connect(self.editMovement)
        self.movementTable.rowDeleted.connect(self.deleteMovement)
        
        # Assembler le layout principal
        layout.addLayout(filterLayout)
        layout.addWidget(self.movementTable)
        
    def loadData(self):
        """Charge les données des mouvements de stock depuis la base de données."""
        # Données de test (à remplacer par des données réelles de la base de données)
        test_data = [
            ["MVT-001", "01/01/2023", "Entrée", "Ordinateur portable", "+10", "Magasin principal", "admin"],
            ["MVT-002", "15/01/2023", "Sortie", "Imprimante laser", "-2", "Magasin principal", "admin"],
            ["MVT-003", "01/02/2023", "Entrée", "Souris sans fil", "+20", "Dépôt secondaire", "admin"],
            ["MVT-004", "15/02/2023", "Ajustement", "Clavier mécanique", "-3", "Magasin principal", "admin"],
            ["MVT-005", "01/03/2023", "Entrée", "Écran 24 pouces", "+5", "Dépôt secondaire", "admin"],
        ]
        
        self.movementTable.setData(test_data)
        
    def onPeriodChanged(self, index):
        """Appelé lorsque la période sélectionnée change."""
        # Afficher/masquer les champs de date personnalisée
        custom_visible = index == 4  # "Personnalisé"
        self.startDateEdit.setVisible(custom_visible)
        self.endDateEdit.setVisible(custom_visible)
        
        # Appliquer les filtres
        self.applyFilters()
        
    def applyFilters(self):
        """Applique les filtres sélectionnés."""
        # Ici, vous devriez filtrer les données en fonction des critères sélectionnés
        # Pour l'instant, nous allons simplement afficher une notification
        
        period = self.periodCombo.currentText()
        movement_type = self.typeCombo.currentText()
        product = self.productCombo.currentText()
        
        InfoBar.success(
            title="Filtres appliqués",
            content=f"Période: {period}, Type: {movement_type}, Produit: {product}",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )
        
    def openMovementForm(self):
        """Ouvre le formulaire d'ajout de mouvement de stock."""
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter un mouvement de stock")
        dialog.setMinimumWidth(600)
        
        layout = QVBoxLayout(dialog)
        
        movementForm = StockMovementForm(parent=dialog)
        movementForm.formSubmitted.connect(lambda data: self.saveMovement(data, dialog))
        movementForm.formCancelled.connect(dialog.reject)
        
        layout.addWidget(movementForm)
        
        dialog.exec_()
        
    def editMovement(self, row, data):
        """Ouvre le formulaire d'édition de mouvement de stock."""
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier un mouvement de stock")
        dialog.setMinimumWidth(600)
        
        layout = QVBoxLayout(dialog)
        
        movementForm = StockMovementForm(parent=dialog)
        
        # Convertir les données de la ligne en dictionnaire
        movement_data = {
            "reference": data[0],
            "date": data[1],
            "type": data[2],
            "product": data[3],
            "quantity": data[4].replace("+", "").replace("-", ""),
            "location": data[5],
            "user": data[6]
        }
        
        movementForm.setFormData(movement_data)
        movementForm.formSubmitted.connect(lambda data: self.updateMovement(row, data, dialog))
        movementForm.formCancelled.connect(dialog.reject)
        
        layout.addWidget(movementForm)
        
        dialog.exec_()
        
    def saveMovement(self, data, dialog):
        """Enregistre un nouveau mouvement de stock."""
        # Ici, vous devriez enregistrer les données dans la base de données
        # Pour l'instant, nous allons simplement les ajouter au tableau
        
        # Formater les données pour l'affichage
        quantity_prefix = "+" if data["type"] == "Entrée" else "-"
        
        row_data = [
            data["reference"],
            data["date"],
            data["type"],
            data["product"],
            f"{quantity_prefix}{data['quantity']}",
            data["location"],
            data["user"]
        ]
        
        # Ajouter au modèle
        model = self.movementTable.model
        model.insertRows(len(model._data), 1)
        for col, value in enumerate(row_data):
            model.setData(model.index(len(model._data) - 1, col), value)
        
        # Fermer le dialogue
        dialog.accept()
        
        # Afficher une notification
        InfoBar.success(
            title="Mouvement ajouté",
            content=f"Le mouvement {data['reference']} a été ajouté avec succès",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )
        
    def updateMovement(self, row, data, dialog):
        """Met à jour un mouvement de stock existant."""
        # Ici, vous devriez mettre à jour les données dans la base de données
        # Pour l'instant, nous allons simplement mettre à jour le tableau
        
        # Formater les données pour l'affichage
        quantity_prefix = "+" if data["type"] == "Entrée" else "-"
        
        row_data = [
            data["reference"],
            data["date"],
            data["type"],
            data["product"],
            f"{quantity_prefix}{data['quantity']}",
            data["location"],
            data["user"]
        ]
        
        # Mettre à jour le modèle
        model = self.movementTable.model
        for col, value in enumerate(row_data):
            source_index = self.movementTable.proxyModel.mapToSource(
                self.movementTable.proxyModel.index(row, col)
            )
            model.setData(source_index, value)
        
        # Fermer le dialogue
        dialog.accept()
        
        # Afficher une notification
        InfoBar.success(
            title="Mouvement modifié",
            content=f"Le mouvement {data['reference']} a été modifié avec succès",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )
        
    def deleteMovement(self, row):
        """Supprime un mouvement de stock."""
        # Ici, vous devriez supprimer le mouvement de la base de données
        # Pour l'instant, nous allons simplement afficher une notification
        
        InfoBar.success(
            title="Mouvement supprimé",
            content="Le mouvement a été supprimé avec succès",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )
