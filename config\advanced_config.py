"""
Configuration avancée pour GSCOM.
Paramètres pour les nouvelles fonctionnalités.
"""
import os

# Version de l'application
VERSION = "2.0.0"
APP_NAME = "GSCOM - Gestion Commerciale"

# Chemins de base
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATA_DIR = os.path.join(BASE_DIR, "data")
CONFIG_DIR = os.path.join(BASE_DIR, "config")
LOGS_DIR = os.path.join(BASE_DIR, "logs")
BACKUP_DIR = os.path.join(BASE_DIR, "backups")
TEMP_DIR = os.path.join(BASE_DIR, "temp")

# Base de données
DATABASE_PATH = os.path.join(DATA_DIR, "gscom.db")

# Sauvegarde automatique
AUTO_BACKUP_ENABLED = True
BACKUP_INTERVAL_HOURS = 24  # Sauvegarde toutes les 24 heures
MAX_BACKUPS = 10  # Garder maximum 10 sauvegardes

# Thèmes
DEFAULT_THEME = "DARK_BLUE"
THEME_CONFIG_FILE = os.path.join(CONFIG_DIR, "theme.json")

# Raccourcis clavier
SHORTCUTS_CONFIG_FILE = os.path.join(CONFIG_DIR, "shortcuts.json")

# Recherche globale
SEARCH_MAX_RESULTS = 50
SEARCH_MIN_QUERY_LENGTH = 2

# Notifications
NOTIFICATION_DURATION_SUCCESS = 3000  # 3 secondes
NOTIFICATION_DURATION_ERROR = 5000    # 5 secondes
NOTIFICATION_DURATION_WARNING = 4000  # 4 secondes
NOTIFICATION_DURATION_INFO = 3000     # 3 secondes
MAX_NOTIFICATIONS = 5

# Interface utilisateur
WINDOW_MIN_WIDTH = 1000
WINDOW_MIN_HEIGHT = 700
WINDOW_DEFAULT_WIDTH = 1200
WINDOW_DEFAULT_HEIGHT = 800

# Animations
ANIMATION_DURATION_FAST = 200
ANIMATION_DURATION_NORMAL = 300
ANIMATION_DURATION_SLOW = 500

# Graphiques
CHART_UPDATE_INTERVAL = 30000  # 30 secondes
CHART_MAX_DATA_POINTS = 50
CHART_ANIMATION_DURATION = 1000

# Performance
LAZY_LOADING_ENABLED = True
CACHE_SIZE_MB = 50
AUTO_SAVE_INTERVAL = 300000  # 5 minutes

# Sécurité
SESSION_TIMEOUT_MINUTES = 60
AUTO_LOCK_ENABLED = False
AUTO_LOCK_TIMEOUT_MINUTES = 15

# Logs
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_MAX_SIZE_MB = 10
LOG_BACKUP_COUNT = 5

# Export/Import
EXPORT_DEFAULT_FORMAT = "PDF"
EXPORT_FORMATS = ["PDF", "Excel", "CSV", "HTML"]
IMPORT_MAX_FILE_SIZE_MB = 100

# Rapports
REPORT_CACHE_ENABLED = True
REPORT_CACHE_DURATION_HOURS = 1
REPORT_MAX_ROWS = 10000

# Modules activés
MODULES_ENABLED = {
    "dashboard": True,
    "products": True,
    "customers": True,
    "suppliers": True,
    "sales": True,
    "inventory": True,
    "reports": True,
    "administration": True,
    "advanced_dashboard": True,
    "global_search": True,
    "backup_manager": True,
    "keyboard_shortcuts": True,
    "modern_themes": True,
    "notifications": True
}

# Fonctionnalités expérimentales
EXPERIMENTAL_FEATURES = {
    "ai_suggestions": False,
    "voice_commands": False,
    "mobile_sync": False,
    "cloud_backup": False,
    "advanced_analytics": False
}

# API et intégrations
API_ENABLED = False
API_PORT = 8080
API_HOST = "localhost"

# Mise à jour automatique
AUTO_UPDATE_ENABLED = False
UPDATE_CHECK_INTERVAL_HOURS = 24
UPDATE_SERVER_URL = "https://updates.gscom.local"

# Personnalisation
CUSTOM_CSS_ENABLED = True
CUSTOM_CSS_FILE = os.path.join(CONFIG_DIR, "custom.css")

# Plugins
PLUGINS_ENABLED = False
PLUGINS_DIR = os.path.join(BASE_DIR, "plugins")

# Statistiques d'utilisation
USAGE_STATS_ENABLED = True
USAGE_STATS_FILE = os.path.join(DATA_DIR, "usage_stats.json")

# Fonctions utilitaires
def ensure_directories():
    """Crée les dossiers nécessaires s'ils n'existent pas."""
    directories = [DATA_DIR, CONFIG_DIR, LOGS_DIR, BACKUP_DIR, TEMP_DIR]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"INFO: Dossier créé: {directory}")

def get_config_value(key, default=None):
    """Récupère une valeur de configuration."""
    return globals().get(key, default)

def set_config_value(key, value):
    """Définit une valeur de configuration."""
    globals()[key] = value

def is_module_enabled(module_name):
    """Vérifie si un module est activé."""
    return MODULES_ENABLED.get(module_name, False)

def is_feature_enabled(feature_name):
    """Vérifie si une fonctionnalité expérimentale est activée."""
    return EXPERIMENTAL_FEATURES.get(feature_name, False)

def get_database_url():
    """Retourne l'URL de la base de données."""
    return f"sqlite:///{DATABASE_PATH}"

def get_log_config():
    """Retourne la configuration des logs."""
    return {
        "level": LOG_LEVEL,
        "max_size_mb": LOG_MAX_SIZE_MB,
        "backup_count": LOG_BACKUP_COUNT,
        "directory": LOGS_DIR
    }

def get_backup_config():
    """Retourne la configuration de sauvegarde."""
    return {
        "enabled": AUTO_BACKUP_ENABLED,
        "interval_hours": BACKUP_INTERVAL_HOURS,
        "max_backups": MAX_BACKUPS,
        "directory": BACKUP_DIR
    }

def get_ui_config():
    """Retourne la configuration de l'interface utilisateur."""
    return {
        "min_width": WINDOW_MIN_WIDTH,
        "min_height": WINDOW_MIN_HEIGHT,
        "default_width": WINDOW_DEFAULT_WIDTH,
        "default_height": WINDOW_DEFAULT_HEIGHT,
        "animation_duration": ANIMATION_DURATION_NORMAL
    }

def get_notification_config():
    """Retourne la configuration des notifications."""
    return {
        "duration_success": NOTIFICATION_DURATION_SUCCESS,
        "duration_error": NOTIFICATION_DURATION_ERROR,
        "duration_warning": NOTIFICATION_DURATION_WARNING,
        "duration_info": NOTIFICATION_DURATION_INFO,
        "max_notifications": MAX_NOTIFICATIONS
    }

# Initialisation
if __name__ == "__main__":
    ensure_directories()
    print("Configuration avancée initialisée.")
    print(f"Version: {VERSION}")
    print(f"Dossier de base: {BASE_DIR}")
    print(f"Base de données: {DATABASE_PATH}")
    print(f"Modules activés: {sum(MODULES_ENABLED.values())}/{len(MODULES_ENABLED)}")
