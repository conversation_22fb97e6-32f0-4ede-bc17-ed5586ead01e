"""
Tableau de bord avancé avec graphiques interactifs pour GSCOM.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QTabWidget, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QFont

from qfluentwidgets import FluentIcon, IconWidget, PushButton, CardWidget
from ui.components.charts import <PERSON>Chart, DonutChart, BarChart
from ui.dashboard.modern_dashboard import StatCard, QuickActionButton, ActivityFeed
from ui.themes.modern_themes import modern_theme_manager
from ui.components.modern_notifications import show_success, show_info
import random
import datetime


class MetricCard(CardWidget):
    """Carte de métrique avancée avec tendance."""
    
    def __init__(self, title, value, trend=0, icon=FluentIcon.INFO, color="#3B82F6", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.trend = trend
        self.icon = icon
        self.color = color
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface de la carte."""
        self.setFixedSize(200, 120)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(8)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        # Icône
        icon_widget = IconWidget(self.icon, self)
        icon_widget.setFixedSize(24, 24)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setObjectName("metricTitle")
        
        header_layout.addWidget(icon_widget)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Valeur principale
        value_label = QLabel(str(self.value))
        value_label.setObjectName("metricValue")
        value_label.setAlignment(Qt.AlignLeft)
        
        # Tendance
        trend_layout = QHBoxLayout()
        trend_icon = "↗" if self.trend > 0 else "↘" if self.trend < 0 else "→"
        trend_color = "#10B981" if self.trend > 0 else "#EF4444" if self.trend < 0 else "#6B7280"
        
        trend_label = QLabel(f"{trend_icon} {abs(self.trend):.1f}%")
        trend_label.setObjectName("metricTrend")
        trend_label.setStyleSheet(f"color: {trend_color}; font-size: 11px; font-weight: 500;")
        
        trend_layout.addWidget(trend_label)
        trend_layout.addStretch()
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addLayout(trend_layout)
        
        # Style
        colors = modern_theme_manager.get_colors()
        self.setStyleSheet(f"""
            MetricCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}15,
                    stop:1 {self.color}08);
                border: 1px solid {self.color}30;
                border-radius: 12px;
            }}
            MetricCard:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}25,
                    stop:1 {self.color}15);
                border: 1px solid {self.color}50;
            }}
            QLabel#metricTitle {{
                color: {colors['text_secondary']};
                font-size: 12px;
                font-weight: 500;
            }}
            QLabel#metricValue {{
                color: {self.color};
                font-size: 24px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
        """)


class ChartContainer(QFrame):
    """Conteneur pour graphiques avec titre et contrôles."""
    
    def __init__(self, title, chart_widget, parent=None):
        super().__init__(parent)
        self.title = title
        self.chart_widget = chart_widget
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface du conteneur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # En-tête avec titre et contrôles
        header_layout = QHBoxLayout()
        
        title_label = QLabel(self.title)
        title_label.setObjectName("chartTitle")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        
        # Boutons de contrôle
        refresh_btn = PushButton()
        refresh_btn.setIcon(FluentIcon.SYNC.icon())
        refresh_btn.setFixedSize(32, 32)
        refresh_btn.clicked.connect(self.refresh_chart)
        
        fullscreen_btn = PushButton()
        fullscreen_btn.setIcon(FluentIcon.FULL_SCREEN.icon())
        fullscreen_btn.setFixedSize(32, 32)
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(refresh_btn)
        header_layout.addWidget(fullscreen_btn)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.chart_widget)
        
        # Style
        colors = modern_theme_manager.get_colors()
        self.setStyleSheet(f"""
            ChartContainer {{
                background: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 12px;
            }}
            QLabel#chartTitle {{
                color: {colors['text']};
            }}
        """)
        
    def refresh_chart(self):
        """Actualise le graphique."""
        if hasattr(self.chart_widget, 'add_data_point'):
            # Ajouter une nouvelle donnée simulée
            new_value = random.randint(80000, 150000)
            self.chart_widget.add_data_point(new_value)
            
        show_info("Graphique actualisé", f"Le graphique '{self.title}' a été mis à jour")
        
    def toggle_fullscreen(self):
        """Bascule en mode plein écran."""
        show_info("Mode plein écran", "Fonctionnalité à implémenter")


class AdvancedDashboard(QWidget):
    """Tableau de bord avancé avec graphiques et métriques."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        self.setupTimers()
        self.loadData()
        
        # Connecter le signal de changement de thème
        modern_theme_manager.themeChanged.connect(self.onThemeChanged)
        
    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        header_layout = self.createHeader()
        layout.addLayout(header_layout)
        
        # Métriques principales
        metrics_layout = self.createMetricsSection()
        layout.addLayout(metrics_layout)
        
        # Onglets de graphiques
        charts_tabs = self.createChartsSection()
        layout.addWidget(charts_tabs)
        
        # Section inférieure avec activités et actions
        bottom_layout = self.createBottomSection()
        layout.addLayout(bottom_layout)
        
    def createHeader(self):
        """Crée l'en-tête du dashboard."""
        layout = QHBoxLayout()
        
        # Titre et informations
        info_layout = QVBoxLayout()
        
        title_label = QLabel("Dashboard Avancé")
        title_label.setObjectName("dashboardTitle")
        title_label.setFont(QFont("Segoe UI", 28, QFont.Bold))
        
        subtitle_label = QLabel("Vue d'ensemble des performances")
        subtitle_label.setObjectName("dashboardSubtitle")
        subtitle_label.setFont(QFont("Segoe UI", 14))
        
        date_label = QLabel(datetime.datetime.now().strftime("%A, %d %B %Y"))
        date_label.setObjectName("dateLabel")
        date_label.setFont(QFont("Segoe UI", 12))
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        info_layout.addWidget(date_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # Boutons d'action
        export_btn = PushButton("Exporter")
        export_btn.setIcon(FluentIcon.DOWNLOAD.icon())
        export_btn.clicked.connect(self.export_dashboard)
        
        settings_btn = PushButton("Paramètres")
        settings_btn.setIcon(FluentIcon.SETTING.icon())
        settings_btn.clicked.connect(self.open_settings)
        
        layout.addWidget(export_btn)
        layout.addWidget(settings_btn)
        
        return layout
        
    def createMetricsSection(self):
        """Crée la section des métriques."""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # Métriques avec tendances
        metrics = [
            {
                "title": "Chiffre d'affaires",
                "value": "245 670 DA",
                "trend": 12.5,
                "icon": FluentIcon.MONEY,
                "color": "#10B981"
            },
            {
                "title": "Commandes",
                "value": "156",
                "trend": 8.3,
                "icon": FluentIcon.SHOPPING_CART,
                "color": "#3B82F6"
            },
            {
                "title": "Clients actifs",
                "value": "89",
                "trend": -2.1,
                "icon": FluentIcon.PEOPLE,
                "color": "#8B5CF6"
            },
            {
                "title": "Taux conversion",
                "value": "3.2%",
                "trend": 5.7,
                "icon": FluentIcon.CHART,
                "color": "#F59E0B"
            },
            {
                "title": "Stock critique",
                "value": "12",
                "trend": -15.4,
                "icon": FluentIcon.CARE_RIGHT_SOLID,
                "color": "#EF4444"
            }
        ]
        
        for metric in metrics:
            card = MetricCard(
                metric["title"],
                metric["value"],
                metric["trend"],
                metric["icon"],
                metric["color"]
            )
            layout.addWidget(card)
            
        layout.addStretch()
        return layout
        
    def createChartsSection(self):
        """Crée la section des graphiques avec onglets."""
        tabs = QTabWidget()
        
        # Onglet Ventes
        sales_tab = QWidget()
        sales_layout = QGridLayout(sales_tab)
        sales_layout.setSpacing(15)
        
        # Graphique des ventes
        self.sales_chart = SalesChart()
        sales_container = ChartContainer("Évolution des Ventes", self.sales_chart)
        sales_layout.addWidget(sales_container, 0, 0, 1, 2)
        
        # Graphique en donut
        self.category_chart = DonutChart("Répartition par Catégorie")
        category_container = ChartContainer("Catégories", self.category_chart)
        sales_layout.addWidget(category_container, 1, 0)
        
        # Graphique en barres
        self.monthly_chart = BarChart("Ventes Mensuelles")
        monthly_container = ChartContainer("Mensuel", self.monthly_chart)
        sales_layout.addWidget(monthly_container, 1, 1)
        
        tabs.addTab(sales_tab, "📊 Ventes")
        
        # Onglet Inventaire
        inventory_tab = QWidget()
        inventory_layout = QVBoxLayout(inventory_tab)
        
        inventory_label = QLabel("Graphiques d'inventaire à implémenter")
        inventory_label.setAlignment(Qt.AlignCenter)
        inventory_label.setStyleSheet("color: #6B7280; font-size: 16px;")
        inventory_layout.addWidget(inventory_label)
        
        tabs.addTab(inventory_tab, "📦 Inventaire")
        
        # Onglet Finances
        finance_tab = QWidget()
        finance_layout = QVBoxLayout(finance_tab)
        
        finance_label = QLabel("Graphiques financiers à implémenter")
        finance_label.setAlignment(Qt.AlignCenter)
        finance_label.setStyleSheet("color: #6B7280; font-size: 16px;")
        finance_layout.addWidget(finance_label)
        
        tabs.addTab(finance_tab, "💰 Finances")
        
        return tabs
        
    def createBottomSection(self):
        """Crée la section inférieure."""
        layout = QHBoxLayout()
        layout.setSpacing(20)
        
        # Flux d'activité
        self.activity_feed = ActivityFeed()
        layout.addWidget(self.activity_feed, 1)
        
        # Actions rapides améliorées
        actions_frame = QFrame()
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(15, 15, 15, 15)
        
        actions_title = QLabel("Actions Rapides")
        actions_title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        actions_layout.addWidget(actions_title)
        
        # Grille d'actions
        actions_grid = QGridLayout()
        actions_grid.setSpacing(10)
        
        actions = [
            ("Nouvelle Vente", FluentIcon.ADD, "#10B981", self.new_sale),
            ("Nouveau Client", FluentIcon.PEOPLE, "#3B82F6", self.new_customer),
            ("Gestion Stock", FluentIcon.PACKAGE, "#F59E0B", self.manage_inventory),
            ("Rapports", FluentIcon.DOCUMENT, "#8B5CF6", self.generate_reports),
            ("Paramètres", FluentIcon.SETTING, "#6B7280", self.open_settings),
            ("Aide", FluentIcon.HELP, "#EF4444", self.show_help)
        ]
        
        for i, (text, icon, color, callback) in enumerate(actions):
            btn = QuickActionButton(text, icon, color)
            btn.clicked.connect(callback)
            actions_grid.addWidget(btn, i // 2, i % 2)
            
        actions_layout.addLayout(actions_grid)
        actions_layout.addStretch()
        
        # Style du frame
        colors = modern_theme_manager.get_colors()
        actions_frame.setStyleSheet(f"""
            QFrame {{
                background: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 12px;
            }}
            QLabel {{
                color: {colors['text']};
            }}
        """)
        
        layout.addWidget(actions_frame, 1)
        
        return layout
        
    def setupTimers(self):
        """Configure les timers pour les mises à jour."""
        # Timer pour les métriques
        self.metrics_timer = QTimer()
        self.metrics_timer.timeout.connect(self.updateMetrics)
        self.metrics_timer.start(45000)  # 45 secondes
        
        # Timer pour les graphiques
        self.charts_timer = QTimer()
        self.charts_timer.timeout.connect(self.updateCharts)
        self.charts_timer.start(60000)  # 1 minute
        
    def loadData(self):
        """Charge les données initiales."""
        # Démarrer les animations des graphiques
        QTimer.singleShot(500, self.sales_chart.start_animation)
        QTimer.singleShot(700, self.category_chart.start_animation)
        QTimer.singleShot(900, self.monthly_chart.start_animation)
        
        # Ajouter des activités
        activities = [
            (FluentIcon.SHOPPING_CART, "Vente #VT-2024-001 créée", "Il y a 2 min"),
            (FluentIcon.PEOPLE, "Client 'TechCorp' ajouté", "Il y a 8 min"),
            (FluentIcon.PACKAGE, "Stock produit 'Laptop HP' mis à jour", "Il y a 15 min"),
            (FluentIcon.DOCUMENT, "Rapport mensuel généré", "Il y a 32 min"),
        ]
        
        for icon, text, time_str in activities:
            self.activity_feed.addActivity(icon, text, time_str)
            
    def updateMetrics(self):
        """Met à jour les métriques."""
        show_info("Métriques mises à jour", "Les données ont été actualisées")
        
    def updateCharts(self):
        """Met à jour les graphiques."""
        # Ajouter de nouvelles données aux graphiques
        new_sales_value = random.randint(90000, 140000)
        self.sales_chart.add_data_point(new_sales_value)
        
    # Méthodes d'action
    def export_dashboard(self):
        show_success("Export", "Dashboard exporté avec succès")
        
    def new_sale(self):
        show_info("Nouvelle Vente", "Ouverture du module de vente...")
        
    def new_customer(self):
        show_info("Nouveau Client", "Ouverture du formulaire client...")
        
    def manage_inventory(self):
        show_info("Gestion Stock", "Ouverture du module d'inventaire...")
        
    def generate_reports(self):
        show_info("Rapports", "Ouverture du générateur de rapports...")
        
    def open_settings(self):
        show_info("Paramètres", "Ouverture des paramètres...")
        
    def show_help(self):
        show_info("Aide", "Ouverture de l'aide en ligne...")
        
    @pyqtSlot(object)
    def onThemeChanged(self, theme):
        """Appelé lorsque le thème change."""
        # Mettre à jour les styles
        colors = modern_theme_manager.get_colors()
        
        # Style principal
        self.setStyleSheet(f"""
            AdvancedDashboard {{
                background: {colors['background']};
            }}
            QLabel#dashboardTitle {{
                color: {colors['text']};
            }}
            QLabel#dashboardSubtitle {{
                color: {colors['text_secondary']};
            }}
            QLabel#dateLabel {{
                color: {colors['text_secondary']};
            }}
        """)
        
        # Redessiner les graphiques
        self.sales_chart.update()
        self.category_chart.update()
        self.monthly_chart.update()
