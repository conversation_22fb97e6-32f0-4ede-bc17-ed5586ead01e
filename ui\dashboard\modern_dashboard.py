"""
Dashboard moderne et interactif pour GSCOM.
Interface avec widgets animés, graphiques et statistiques en temps réel.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QScrollArea, QProgressBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, pyqtSlot
from PyQt5.QtGui import QFont, QPainter, QPen, QBrush, QColor, QLinearGradient

from qfluentwidgets import (
    FluentIcon, IconWidget, CardWidget, PushButton,
    InfoBar, InfoBarPosition, ProgressBar, Slider
)

from utils.theme_manager import theme_manager
from ui.styles.clean_style import get_clean_style
import random
import datetime


class StatCard(CardWidget):
    """Carte de statistique animée."""

    clicked = pyqtSignal()

    def __init__(self, title, value, icon, color="#38BDF8", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.setupUI()
        self.setupAnimation()

    def setupUI(self):
        """Configure l'interface de la carte."""
        self.setFixedSize(280, 140)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)

        # En-tête avec icône et titre
        header_layout = QHBoxLayout()

        # Icône
        self.icon_widget = IconWidget(self.icon, self)
        self.icon_widget.setFixedSize(32, 32)

        # Titre
        self.title_label = QLabel(self.title)
        self.title_label.setObjectName("cardTitle")

        header_layout.addWidget(self.icon_widget)
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()

        # Valeur principale
        self.value_label = QLabel(str(self.value))
        self.value_label.setObjectName("cardValue")
        self.value_label.setAlignment(Qt.AlignCenter)

        # Barre de progression (optionnelle)
        self.progress_bar = ProgressBar()
        self.progress_bar.setVisible(False)

        layout.addLayout(header_layout)
        layout.addWidget(self.value_label)
        layout.addWidget(self.progress_bar)
        layout.addStretch()

        # Style de la carte
        self.setStyleSheet(f"""
            StatCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}20,
                    stop:1 {self.color}10);
                border: 1px solid {self.color}40;
                border-radius: 12px;
            }}
            StatCard:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}30,
                    stop:1 {self.color}20);
                border: 1px solid {self.color}60;
            }}
            QLabel#cardTitle {{
                color: #B0C4DE;
                font-size: 14px;
                font-weight: 500;
            }}
            QLabel#cardValue {{
                color: {self.color};
                font-size: 28px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
        """)

    def setupAnimation(self):
        """Configure les animations de la carte."""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)

    def updateValue(self, new_value):
        """Met à jour la valeur avec animation."""
        self.value = new_value
        self.value_label.setText(str(new_value))

        # Animation de pulsation
        self.pulse_animation = QPropertyAnimation(self.value_label, b"geometry")
        self.pulse_animation.setDuration(300)
        self.pulse_animation.setEasingCurve(QEasingCurve.OutBounce)

        original_rect = self.value_label.geometry()
        expanded_rect = original_rect.adjusted(-5, -5, 5, 5)

        self.pulse_animation.setStartValue(original_rect)
        self.pulse_animation.setEndValue(expanded_rect)
        self.pulse_animation.finished.connect(
            lambda: QPropertyAnimation(self.value_label, b"geometry").setEndValue(original_rect)
        )
        self.pulse_animation.start()

    def showProgress(self, value, maximum=100):
        """Affiche une barre de progression."""
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(maximum)
        self.progress_bar.setValue(value)

    def hideProgress(self):
        """Cache la barre de progression."""
        self.progress_bar.setVisible(False)

    def mousePressEvent(self, event):
        """Gère le clic sur la carte."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class QuickActionButton(PushButton):
    """Bouton d'action rapide avec icône."""

    def __init__(self, text, icon, color="#38BDF8", parent=None):
        super().__init__(text, parent)
        self.setIcon(icon.icon())
        self.color = color
        self.setupStyle()

    def setupStyle(self):
        """Configure le style du bouton."""
        self.setFixedSize(200, 50)
        self.setStyleSheet(f"""
            QuickActionButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.color},
                    stop:1 {self.color}CC);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 0 15px;
            }}
            QuickActionButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.color}DD,
                    stop:1 {self.color}AA);
            }}
            QuickActionButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.color}BB,
                    stop:1 {self.color}88);
            }}
        """)


class ActivityFeed(QFrame):
    """Flux d'activité récente."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        self.activities = []

    def setupUI(self):
        """Configure l'interface du flux d'activité."""
        self.setFixedHeight(300)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Titre
        title_label = QLabel("Activité récente")
        title_label.setObjectName("feedTitle")
        layout.addWidget(title_label)

        # Zone de défilement
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # Widget de contenu
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setSpacing(8)

        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)

        # Style
        self.setStyleSheet("""
            ActivityFeed {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }
            QLabel#feedTitle {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
            }
            QScrollArea {
                border: none;
                background: transparent;
            }
        """)

    def addActivity(self, icon, text, time_str):
        """Ajoute une nouvelle activité."""
        activity_widget = QWidget()
        activity_layout = QHBoxLayout(activity_widget)
        activity_layout.setContentsMargins(10, 8, 10, 8)
        activity_layout.setSpacing(10)

        # Icône
        icon_widget = IconWidget(icon, activity_widget)
        icon_widget.setFixedSize(20, 20)

        # Texte
        text_label = QLabel(text)
        text_label.setObjectName("activityText")
        text_label.setWordWrap(True)

        # Heure
        time_label = QLabel(time_str)
        time_label.setObjectName("activityTime")

        activity_layout.addWidget(icon_widget)
        activity_layout.addWidget(text_label, 1)
        activity_layout.addWidget(time_label)

        # Style de l'activité
        activity_widget.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.03);
                border-radius: 6px;
            }
            QWidget:hover {
                background: rgba(255, 255, 255, 0.08);
            }
            QLabel#activityText {
                color: #E2E8F0;
                font-size: 13px;
            }
            QLabel#activityTime {
                color: #94A3B8;
                font-size: 11px;
            }
        """)

        # Ajouter au début de la liste
        self.content_layout.insertWidget(0, activity_widget)
        self.activities.insert(0, activity_widget)

        # Limiter le nombre d'activités affichées
        if len(self.activities) > 10:
            old_activity = self.activities.pop()
            old_activity.deleteLater()


class ModernDashboard(QWidget):
    """Dashboard moderne avec widgets interactifs."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        self.setupTimers()
        self.loadData()

        # Connecter le signal de changement de thème
        theme_manager.themeChanged.connect(self.onThemeChanged)

    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête du dashboard
        header_layout = self.createHeader()
        layout.addLayout(header_layout)

        # Cartes de statistiques
        stats_layout = self.createStatsSection()
        layout.addLayout(stats_layout)

        # Section principale avec graphiques et activités
        main_layout = self.createMainSection()
        layout.addLayout(main_layout)

        # Actions rapides
        actions_layout = self.createQuickActions()
        layout.addLayout(actions_layout)

        # Appliquer le style
        self.updateStyle()

    def createHeader(self):
        """Crée l'en-tête du dashboard."""
        layout = QHBoxLayout()

        # Titre et date
        title_layout = QVBoxLayout()

        title_label = QLabel("Dashboard")
        title_label.setObjectName("dashboardTitle")

        date_label = QLabel(datetime.datetime.now().strftime("%A, %d %B %Y"))
        date_label.setObjectName("dateLabel")

        title_layout.addWidget(title_label)
        title_layout.addWidget(date_label)

        layout.addLayout(title_layout)
        layout.addStretch()

        # Bouton de rafraîchissement
        refresh_button = PushButton("Actualiser")
        refresh_button.setIcon(FluentIcon.SYNC.icon())
        refresh_button.clicked.connect(self.refreshData)

        layout.addWidget(refresh_button)

        return layout

    def createStatsSection(self):
        """Crée la section des statistiques."""
        layout = QHBoxLayout()
        layout.setSpacing(15)

        # Cartes de statistiques
        self.sales_card = StatCard(
            "Ventes du jour",
            "125 670 DA",
            FluentIcon.SHOPPING_CART,
            "#10B981"
        )
        self.sales_card.clicked.connect(self.openSalesDetails)

        self.orders_card = StatCard(
            "Commandes",
            "47",
            FluentIcon.DOCUMENT,
            "#3B82F6"
        )
        self.orders_card.clicked.connect(self.openOrdersDetails)

        self.customers_card = StatCard(
            "Nouveaux clients",
            "12",
            FluentIcon.PEOPLE,
            "#8B5CF6"
        )
        self.customers_card.clicked.connect(self.openCustomersDetails)

        self.inventory_card = StatCard(
            "Stock critique",
            "8",
            FluentIcon.CARE_RIGHT_SOLID,
            "#EF4444"
        )
        self.inventory_card.clicked.connect(self.openInventoryDetails)

        layout.addWidget(self.sales_card)
        layout.addWidget(self.orders_card)
        layout.addWidget(self.customers_card)
        layout.addWidget(self.inventory_card)
        layout.addStretch()

        return layout

    def createMainSection(self):
        """Crée la section principale avec graphiques et activités."""
        layout = QHBoxLayout()
        layout.setSpacing(20)

        # Graphique des ventes (placeholder)
        chart_frame = QFrame()
        chart_frame.setObjectName("chartFrame")
        chart_frame.setMinimumSize(500, 300)

        chart_layout = QVBoxLayout(chart_frame)
        chart_layout.setContentsMargins(20, 20, 20, 20)

        chart_title = QLabel("Évolution des ventes")
        chart_title.setObjectName("chartTitle")
        chart_layout.addWidget(chart_title)

        # Placeholder pour le graphique
        chart_placeholder = QLabel("Graphique des ventes\n(À implémenter avec matplotlib)")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setObjectName("chartPlaceholder")
        chart_layout.addWidget(chart_placeholder)

        # Flux d'activité
        self.activity_feed = ActivityFeed()

        layout.addWidget(chart_frame, 2)
        layout.addWidget(self.activity_feed, 1)

        return layout

    def createQuickActions(self):
        """Crée la section des actions rapides."""
        layout = QVBoxLayout()

        # Titre
        title_label = QLabel("Actions rapides")
        title_label.setObjectName("sectionTitle")
        layout.addWidget(title_label)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        new_sale_btn = QuickActionButton("Nouvelle vente", FluentIcon.ADD, "#10B981")
        new_sale_btn.clicked.connect(self.newSale)

        new_customer_btn = QuickActionButton("Nouveau client", FluentIcon.PEOPLE, "#3B82F6")
        new_customer_btn.clicked.connect(self.newCustomer)

        inventory_btn = QuickActionButton("Gestion stock", FluentIcon.PACKAGE, "#F59E0B")
        inventory_btn.clicked.connect(self.openInventory)

        reports_btn = QuickActionButton("Rapports", FluentIcon.DOCUMENT, "#8B5CF6")
        reports_btn.clicked.connect(self.openReports)

        buttons_layout.addWidget(new_sale_btn)
        buttons_layout.addWidget(new_customer_btn)
        buttons_layout.addWidget(inventory_btn)
        buttons_layout.addWidget(reports_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        return layout

    def setupTimers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer pour mettre à jour les statistiques
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.updateStats)
        self.stats_timer.start(30000)  # Toutes les 30 secondes

        # Timer pour ajouter des activités simulées
        self.activity_timer = QTimer()
        self.activity_timer.timeout.connect(self.addRandomActivity)
        self.activity_timer.start(10000)  # Toutes les 10 secondes

    def loadData(self):
        """Charge les données initiales."""
        # Ajouter quelques activités initiales
        activities = [
            (FluentIcon.SHOPPING_CART, "Nouvelle vente: Commande #1234", "Il y a 5 min"),
            (FluentIcon.PEOPLE, "Nouveau client: Marie Dupont", "Il y a 12 min"),
            (FluentIcon.PACKAGE, "Stock mis à jour: Produit ABC", "Il y a 18 min"),
            (FluentIcon.DOCUMENT, "Facture générée: #INV-2024-001", "Il y a 25 min"),
        ]

        for icon, text, time_str in activities:
            self.activity_feed.addActivity(icon, text, time_str)

    def updateStats(self):
        """Met à jour les statistiques avec des valeurs simulées."""
        # Simuler des changements de données
        sales_value = random.randint(120000, 150000)
        orders_count = random.randint(40, 55)
        customers_count = random.randint(8, 15)
        critical_stock = random.randint(5, 12)

        self.sales_card.updateValue(f"{sales_value:,} DA".replace(",", " "))
        self.orders_card.updateValue(str(orders_count))
        self.customers_card.updateValue(str(customers_count))
        self.inventory_card.updateValue(str(critical_stock))

    def addRandomActivity(self):
        """Ajoute une activité aléatoire."""
        activities = [
            (FluentIcon.SHOPPING_CART, "Nouvelle vente enregistrée", "À l'instant"),
            (FluentIcon.PEOPLE, "Client mis à jour", "À l'instant"),
            (FluentIcon.PACKAGE, "Mouvement de stock", "À l'instant"),
            (FluentIcon.DOCUMENT, "Rapport généré", "À l'instant"),
            (FluentIcon.SYNC, "Synchronisation effectuée", "À l'instant"),
        ]

        activity = random.choice(activities)
        self.activity_feed.addActivity(*activity)

    def refreshData(self):
        """Actualise toutes les données."""
        self.updateStats()

        InfoBar.success(
            title="Données actualisées",
            content="Le dashboard a été mis à jour avec succès",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    # Slots pour les actions
    def openSalesDetails(self):
        """Ouvre les détails des ventes."""
        InfoBar.info(
            title="Ventes",
            content="Ouverture du module de ventes...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def openOrdersDetails(self):
        """Ouvre les détails des commandes."""
        InfoBar.info(
            title="Commandes",
            content="Ouverture du module de commandes...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def openCustomersDetails(self):
        """Ouvre les détails des clients."""
        InfoBar.info(
            title="Clients",
            content="Ouverture du module de clients...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def openInventoryDetails(self):
        """Ouvre les détails de l'inventaire."""
        InfoBar.info(
            title="Inventaire",
            content="Ouverture du module d'inventaire...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def newSale(self):
        """Crée une nouvelle vente."""
        InfoBar.success(
            title="Nouvelle vente",
            content="Ouverture du formulaire de vente...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def newCustomer(self):
        """Crée un nouveau client."""
        InfoBar.success(
            title="Nouveau client",
            content="Ouverture du formulaire client...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def openInventory(self):
        """Ouvre la gestion de stock."""
        InfoBar.info(
            title="Gestion de stock",
            content="Ouverture du module de stock...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def openReports(self):
        """Ouvre les rapports."""
        InfoBar.info(
            title="Rapports",
            content="Ouverture du générateur de rapports...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self
        )

    def updateStyle(self):
        """Met à jour le style en fonction du thème actuel."""
        self.setStyleSheet("""
            ModernDashboard {
                background: transparent;
            }
            QLabel#dashboardTitle {
                color: #FFFFFF;
                font-size: 32px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel#dateLabel {
                color: #94A3B8;
                font-size: 14px;
            }
            QLabel#sectionTitle {
                color: #FFFFFF;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            QFrame#chartFrame {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }
            QLabel#chartTitle {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
            }
            QLabel#chartPlaceholder {
                color: #64748B;
                font-size: 14px;
            }
        """)

    @pyqtSlot(object)
    def onThemeChanged(self, _):
        """Appelé lorsque le thème change."""
        self.updateStyle()
