"""
Gestionnaire de sauvegarde et restauration pour GSCOM.
Sauvegarde automatique et manuelle des données avec compression.
"""
import os
import shutil
import zipfile
import json
import sqlite3
from datetime import datetime, timedelta
from PyQt5.QtCore import QObject, QTimer, pyqtSignal, QThread
from PyQt5.QtWidgets import QMessageBox, QFileDialog, QProgressDialog
import config


class BackupWorker(QThread):
    """Worker thread pour les opérations de sauvegarde."""
    
    progressChanged = pyqtSignal(int)
    statusChanged = pyqtSignal(str)
    finished = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, operation, source_path, destination_path):
        super().__init__()
        self.operation = operation  # 'backup' ou 'restore'
        self.source_path = source_path
        self.destination_path = destination_path
        
    def run(self):
        """Exécute l'opération de sauvegarde/restauration."""
        try:
            if self.operation == 'backup':
                self.create_backup()
            elif self.operation == 'restore':
                self.restore_backup()
        except Exception as e:
            self.finished.emit(False, str(e))
            
    def create_backup(self):
        """Crée une sauvegarde complète."""
        self.statusChanged.emit("Préparation de la sauvegarde...")
        
        # Créer le dossier de destination
        os.makedirs(os.path.dirname(self.destination_path), exist_ok=True)
        
        # Liste des fichiers à sauvegarder
        files_to_backup = []
        
        # Base de données
        if os.path.exists(config.DATABASE_PATH):
            files_to_backup.append(('database', config.DATABASE_PATH))
            
        # Configuration
        config_dir = 'config'
        if os.path.exists(config_dir):
            for root, dirs, files in os.walk(config_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, '.')
                    files_to_backup.append(('config', file_path, rel_path))
                    
        # Logs
        logs_dir = 'logs'
        if os.path.exists(logs_dir):
            for root, dirs, files in os.walk(logs_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, '.')
                    files_to_backup.append(('logs', file_path, rel_path))
                    
        # Créer l'archive ZIP
        self.statusChanged.emit("Création de l'archive...")
        
        with zipfile.ZipFile(self.destination_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            total_files = len(files_to_backup)
            
            for i, file_info in enumerate(files_to_backup):
                if len(file_info) == 2:
                    file_type, file_path = file_info
                    archive_name = os.path.basename(file_path)
                else:
                    file_type, file_path, archive_name = file_info
                    
                if os.path.exists(file_path):
                    zipf.write(file_path, archive_name)
                    
                # Mettre à jour le progrès
                progress = int((i + 1) / total_files * 100)
                self.progressChanged.emit(progress)
                self.statusChanged.emit(f"Sauvegarde: {os.path.basename(file_path)}")
                
        # Créer les métadonnées de sauvegarde
        metadata = {
            'timestamp': datetime.now().isoformat(),
            'version': getattr(config, 'VERSION', '1.0.0'),
            'files_count': len(files_to_backup),
            'database_size': os.path.getsize(config.DATABASE_PATH) if os.path.exists(config.DATABASE_PATH) else 0
        }
        
        metadata_path = self.destination_path + '.meta'
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
            
        self.finished.emit(True, f"Sauvegarde créée: {self.destination_path}")
        
    def restore_backup(self):
        """Restaure une sauvegarde."""
        self.statusChanged.emit("Vérification de la sauvegarde...")
        
        if not os.path.exists(self.source_path):
            self.finished.emit(False, "Fichier de sauvegarde introuvable")
            return
            
        # Vérifier les métadonnées
        metadata_path = self.source_path + '.meta'
        metadata = {}
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
                
        self.statusChanged.emit("Extraction de la sauvegarde...")
        
        # Créer un dossier temporaire pour l'extraction
        temp_dir = 'temp_restore'
        os.makedirs(temp_dir, exist_ok=True)
        
        try:
            with zipfile.ZipFile(self.source_path, 'r') as zipf:
                files = zipf.namelist()
                total_files = len(files)
                
                for i, file_name in enumerate(files):
                    zipf.extract(file_name, temp_dir)
                    
                    # Mettre à jour le progrès
                    progress = int((i + 1) / total_files * 100)
                    self.progressChanged.emit(progress)
                    self.statusChanged.emit(f"Extraction: {file_name}")
                    
            # Restaurer les fichiers
            self.statusChanged.emit("Restauration des fichiers...")
            
            # Sauvegarder la base de données actuelle
            if os.path.exists(config.DATABASE_PATH):
                backup_db = config.DATABASE_PATH + '.backup'
                shutil.copy2(config.DATABASE_PATH, backup_db)
                
            # Restaurer la base de données
            temp_db = os.path.join(temp_dir, os.path.basename(config.DATABASE_PATH))
            if os.path.exists(temp_db):
                shutil.copy2(temp_db, config.DATABASE_PATH)
                
            # Restaurer la configuration
            temp_config = os.path.join(temp_dir, 'config')
            if os.path.exists(temp_config):
                if os.path.exists('config'):
                    shutil.rmtree('config')
                shutil.copytree(temp_config, 'config')
                
            self.finished.emit(True, "Restauration terminée avec succès")
            
        finally:
            # Nettoyer le dossier temporaire
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)


class BackupManager(QObject):
    """Gestionnaire de sauvegarde automatique et manuelle."""
    
    backupCompleted = pyqtSignal(bool, str)  # success, message
    
    def __init__(self):
        super().__init__()
        self.backup_dir = getattr(config, 'BACKUP_DIR', 'backups')
        self.auto_backup_enabled = getattr(config, 'AUTO_BACKUP_ENABLED', True)
        self.backup_interval = getattr(config, 'BACKUP_INTERVAL_HOURS', 24)
        self.max_backups = getattr(config, 'MAX_BACKUPS', 10)
        
        # Timer pour sauvegarde automatique
        self.auto_backup_timer = QTimer()
        self.auto_backup_timer.timeout.connect(self.create_auto_backup)
        
        if self.auto_backup_enabled:
            self.start_auto_backup()
            
        # Worker thread
        self.backup_worker = None
        
    def start_auto_backup(self):
        """Démarre la sauvegarde automatique."""
        if self.auto_backup_enabled and self.backup_interval > 0:
            interval_ms = self.backup_interval * 60 * 60 * 1000  # Convertir en millisecondes
            self.auto_backup_timer.start(interval_ms)
            print(f"INFO: Sauvegarde automatique activée (toutes les {self.backup_interval}h)")
            
    def stop_auto_backup(self):
        """Arrête la sauvegarde automatique."""
        self.auto_backup_timer.stop()
        print("INFO: Sauvegarde automatique désactivée")
        
    def create_auto_backup(self):
        """Crée une sauvegarde automatique."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"auto_backup_{timestamp}.zip"
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        self.create_backup(backup_path, auto=True)
        
    def create_backup(self, backup_path=None, auto=False):
        """Crée une sauvegarde manuelle ou automatique."""
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"manual_backup_{timestamp}.zip"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
        # Créer le dossier de sauvegarde
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Démarrer le worker
        self.backup_worker = BackupWorker('backup', '.', backup_path)
        self.backup_worker.finished.connect(self.on_backup_finished)
        
        if not auto:
            # Afficher une boîte de dialogue de progression pour les sauvegardes manuelles
            self.progress_dialog = QProgressDialog("Création de la sauvegarde...", "Annuler", 0, 100)
            self.progress_dialog.setWindowTitle("Sauvegarde en cours")
            self.progress_dialog.setModal(True)
            
            self.backup_worker.progressChanged.connect(self.progress_dialog.setValue)
            self.backup_worker.statusChanged.connect(self.progress_dialog.setLabelText)
            self.progress_dialog.canceled.connect(self.cancel_backup)
            
            self.progress_dialog.show()
            
        self.backup_worker.start()
        
    def restore_backup(self, backup_path):
        """Restaure une sauvegarde."""
        # Confirmation
        reply = QMessageBox.question(
            None,
            "Confirmer la restauration",
            "La restauration va remplacer toutes les données actuelles.\n"
            "Êtes-vous sûr de vouloir continuer ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
            
        # Afficher une boîte de dialogue de progression
        self.progress_dialog = QProgressDialog("Restauration en cours...", "Annuler", 0, 100)
        self.progress_dialog.setWindowTitle("Restauration")
        self.progress_dialog.setModal(True)
        
        # Démarrer le worker
        self.backup_worker = BackupWorker('restore', backup_path, '.')
        self.backup_worker.progressChanged.connect(self.progress_dialog.setValue)
        self.backup_worker.statusChanged.connect(self.progress_dialog.setLabelText)
        self.backup_worker.finished.connect(self.on_restore_finished)
        self.progress_dialog.canceled.connect(self.cancel_backup)
        
        self.progress_dialog.show()
        self.backup_worker.start()
        
    def cancel_backup(self):
        """Annule l'opération en cours."""
        if self.backup_worker and self.backup_worker.isRunning():
            self.backup_worker.terminate()
            self.backup_worker.wait()
            
    def on_backup_finished(self, success, message):
        """Appelé quand la sauvegarde est terminée."""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()
            
        if success:
            self.cleanup_old_backups()
            
        self.backupCompleted.emit(success, message)
        
    def on_restore_finished(self, success, message):
        """Appelé quand la restauration est terminée."""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()
            
        if success:
            QMessageBox.information(
                None,
                "Restauration terminée",
                "La restauration a été effectuée avec succès.\n"
                "Redémarrez l'application pour appliquer les changements."
            )
        else:
            QMessageBox.critical(
                None,
                "Erreur de restauration",
                f"Erreur lors de la restauration:\n{message}"
            )
            
    def cleanup_old_backups(self):
        """Supprime les anciennes sauvegardes."""
        if not os.path.exists(self.backup_dir):
            return
            
        # Lister tous les fichiers de sauvegarde
        backup_files = []
        for file in os.listdir(self.backup_dir):
            if file.endswith('.zip'):
                file_path = os.path.join(self.backup_dir, file)
                backup_files.append((file_path, os.path.getmtime(file_path)))
                
        # Trier par date de modification (plus récent en premier)
        backup_files.sort(key=lambda x: x[1], reverse=True)
        
        # Supprimer les sauvegardes excédentaires
        if len(backup_files) > self.max_backups:
            for file_path, _ in backup_files[self.max_backups:]:
                try:
                    os.remove(file_path)
                    # Supprimer aussi le fichier de métadonnées
                    meta_path = file_path + '.meta'
                    if os.path.exists(meta_path):
                        os.remove(meta_path)
                    print(f"INFO: Ancienne sauvegarde supprimée: {file_path}")
                except Exception as e:
                    print(f"ERREUR: Impossible de supprimer {file_path}: {e}")
                    
    def get_backup_list(self):
        """Récupère la liste des sauvegardes disponibles."""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
            
        for file in os.listdir(self.backup_dir):
            if file.endswith('.zip'):
                file_path = os.path.join(self.backup_dir, file)
                meta_path = file_path + '.meta'
                
                backup_info = {
                    'path': file_path,
                    'name': file,
                    'size': os.path.getsize(file_path),
                    'date': datetime.fromtimestamp(os.path.getmtime(file_path)),
                    'metadata': {}
                }
                
                # Charger les métadonnées si disponibles
                if os.path.exists(meta_path):
                    try:
                        with open(meta_path, 'r', encoding='utf-8') as f:
                            backup_info['metadata'] = json.load(f)
                    except Exception:
                        pass
                        
                backups.append(backup_info)
                
        # Trier par date (plus récent en premier)
        backups.sort(key=lambda x: x['date'], reverse=True)
        
        return backups
        
    def export_backup(self, backup_path, destination):
        """Exporte une sauvegarde vers un emplacement externe."""
        try:
            shutil.copy2(backup_path, destination)
            meta_path = backup_path + '.meta'
            if os.path.exists(meta_path):
                dest_meta = destination + '.meta'
                shutil.copy2(meta_path, dest_meta)
            return True, "Sauvegarde exportée avec succès"
        except Exception as e:
            return False, f"Erreur lors de l'export: {e}"
            
    def import_backup(self, source_path):
        """Importe une sauvegarde depuis un emplacement externe."""
        try:
            filename = os.path.basename(source_path)
            dest_path = os.path.join(self.backup_dir, filename)
            
            # Créer le dossier de sauvegarde
            os.makedirs(self.backup_dir, exist_ok=True)
            
            shutil.copy2(source_path, dest_path)
            
            # Copier les métadonnées si disponibles
            source_meta = source_path + '.meta'
            if os.path.exists(source_meta):
                dest_meta = dest_path + '.meta'
                shutil.copy2(source_meta, dest_meta)
                
            return True, "Sauvegarde importée avec succès"
        except Exception as e:
            return False, f"Erreur lors de l'import: {e}"


# Instance globale du gestionnaire de sauvegarde
backup_manager = BackupManager()
