"""
Tests pour les nouvelles fonctionnalités avancées de GSCOM.
Vérifie que tous les composants modernes fonctionnent correctement.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt


def test_modern_themes():
    """Test du système de thèmes modernes."""
    print("🎨 Test du système de thèmes modernes...")

    from ui.themes.modern_themes import ModernTheme, ModernThemeManager, ThemeColors

    # Test de création du gestionnaire
    theme_manager = ModernThemeManager()
    assert theme_manager is not None

    # Test des thèmes disponibles
    assert ModernTheme.DARK_BLUE in ModernTheme
    assert ModernTheme.CYBERPUNK in ModernTheme
    assert ModernTheme.LIGHT_BLUE in ModernTheme

    # Test de changement de thème
    theme_manager.set_theme(ModernTheme.DARK_PURPLE)
    assert theme_manager.get_current_theme() == ModernTheme.DARK_PURPLE

    # Test des couleurs
    colors = theme_manager.get_colors()
    assert 'primary' in colors
    assert 'background' in colors
    assert 'text' in colors

    # Test de génération de styles
    stylesheet = theme_manager.get_stylesheet("main")
    assert "background-color" in stylesheet

    print("✅ Système de thèmes modernes : OK")


def test_keyboard_shortcuts():
    """Test du système de raccourcis clavier."""
    print("⌨️ Test du système de raccourcis clavier...")

    from ui.components.keyboard_shortcuts import ShortcutManager, ShortcutAction

    # Test de création du gestionnaire
    manager = ShortcutManager()
    assert manager is not None

    # Test d'enregistrement d'action
    manager.register_action("test_action", "Action de test", "Test", "Ctrl+T")
    assert "test_action" in manager.actions

    # Test de récupération de raccourci
    shortcut = manager.get_shortcut("test_action")
    assert shortcut == "Ctrl+T"

    # Test de modification de raccourci
    manager.set_shortcut("test_action", "Ctrl+Shift+T")
    assert manager.get_shortcut("test_action") == "Ctrl+Shift+T"

    # Test des catégories
    categories = manager.get_all_categories()
    assert "Test" in categories

    print("✅ Système de raccourcis clavier : OK")


def test_global_search():
    """Test du système de recherche globale."""
    print("🔍 Test du système de recherche globale...")

    from ui.components.global_search import SearchResult, SearchWorker

    # Test de création de résultat
    result = SearchResult(
        title="Test Product",
        description="Description du produit test",
        category="Produits",
        data={"id": 1}
    )
    assert result.title == "Test Product"
    assert result.category == "Produits"

    # Test de données de recherche
    data_sources = {
        "Produits": [
            {
                "title": "Ordinateur Portable",
                "description": "Laptop HP",
                "keywords": ["ordinateur", "laptop", "hp"]
            }
        ]
    }

    # Test de worker (sans exécution réelle)
    worker = SearchWorker("ordinateur", data_sources)
    assert worker.query == "ordinateur"

    print("✅ Système de recherche globale : OK")


def test_backup_manager():
    """Test du gestionnaire de sauvegarde."""
    print("💾 Test du gestionnaire de sauvegarde...")

    from utils.backup_manager import BackupManager

    # Test de création du gestionnaire
    manager = BackupManager()
    assert manager is not None

    # Test de configuration
    assert hasattr(manager, 'backup_dir')
    assert hasattr(manager, 'auto_backup_enabled')
    assert hasattr(manager, 'max_backups')

    # Test de liste des sauvegardes (peut être vide)
    backups = manager.get_backup_list()
    assert isinstance(backups, list)

    print("✅ Gestionnaire de sauvegarde : OK")


def test_charts():
    """Test des composants de graphiques."""
    print("📊 Test des composants de graphiques...")

    from ui.components.charts import SalesChart, DonutChart, BarChart, AnimatedChart

    # Test de création des graphiques (sans affichage)
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    # Test du graphique de base
    base_chart = AnimatedChart("Test Chart")
    assert base_chart.title == "Test Chart"
    assert base_chart.max_points == 30

    # Test du graphique des ventes
    sales_chart = SalesChart()
    assert sales_chart.title == "Évolution des Ventes"
    assert len(sales_chart.data_points) > 0

    # Test du graphique donut
    donut_chart = DonutChart("Test Donut")
    assert donut_chart.title == "Test Donut"
    assert len(donut_chart.segments) > 0

    # Test du graphique en barres
    bar_chart = BarChart("Test Bars")
    assert bar_chart.title == "Test Bars"
    assert len(bar_chart.bars) > 0

    print("✅ Composants de graphiques : OK")


def test_notifications():
    """Test du système de notifications."""
    print("🔔 Test du système de notifications...")

    from ui.components.modern_notifications import (
        NotificationType, ModernNotification, NotificationManager
    )

    # Test des types de notifications
    assert NotificationType.SUCCESS.value == "success"
    assert NotificationType.ERROR.value == "error"

    # Test de création d'une notification (sans affichage)
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    notification = ModernNotification(
        "Test Title",
        "Test Message",
        NotificationType.INFO,
        duration=1000
    )
    assert notification.title == "Test Title"
    assert notification.message == "Test Message"
    assert notification.notification_type == NotificationType.INFO

    # Test du gestionnaire
    manager = NotificationManager()
    assert manager is not None
    assert manager.max_notifications == 5

    print("✅ Système de notifications : OK")


def test_advanced_config():
    """Test de la configuration avancée."""
    print("⚙️ Test de la configuration avancée...")

    from config.advanced_config import (
        VERSION, APP_NAME, get_config_value, is_module_enabled,
        get_ui_config, get_notification_config
    )

    # Test des constantes
    assert VERSION is not None
    assert APP_NAME is not None

    # Test de récupération de valeur
    version = get_config_value("VERSION")
    assert version is not None

    # Test de vérification de module
    dashboard_enabled = is_module_enabled("dashboard")
    assert isinstance(dashboard_enabled, bool)

    # Test de configuration UI
    ui_config = get_ui_config()
    assert "min_width" in ui_config
    assert "min_height" in ui_config

    # Test de configuration notifications
    notif_config = get_notification_config()
    assert "duration_success" in notif_config
    assert "max_notifications" in notif_config

    print("✅ Configuration avancée : OK")


def run_all_tests():
    """Exécute tous les tests."""
    print("🚀 Démarrage des tests des fonctionnalités avancées GSCOM\n")

    try:
        test_modern_themes()
        test_keyboard_shortcuts()
        test_global_search()
        test_backup_manager()
        test_charts()
        test_notifications()
        test_advanced_config()

        print("\n🎉 Tous les tests sont passés avec succès !")
        print("✅ Les nouvelles fonctionnalités avancées de GSCOM fonctionnent correctement.")

        return True

    except Exception as e:
        print(f"\n❌ Erreur lors des tests : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Créer une application Qt pour les tests qui en ont besoin
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    success = run_all_tests()

    if success:
        print("\n📋 Résumé des fonctionnalités testées :")
        print("   🎨 Système de thèmes modernes (10 thèmes)")
        print("   ⌨️ Raccourcis clavier configurables")
        print("   🔍 Recherche globale intelligente")
        print("   💾 Gestionnaire de sauvegarde avancé")
        print("   📊 Graphiques interactifs animés")
        print("   🔔 Notifications modernes")
        print("   ⚙️ Configuration avancée modulaire")
        print("\n🏆 GSCOM est prêt pour un usage professionnel !")

        sys.exit(0)
    else:
        print("\n💥 Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        sys.exit(1)
