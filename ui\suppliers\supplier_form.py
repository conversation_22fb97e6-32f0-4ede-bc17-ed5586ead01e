"""
Formulaire de fournisseur pour l'application GSCOM.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QTextEdit, QPushButton, QLabel, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from qfluentwidgets import FluentIcon, IconWidget
from ..components.base_form import BaseForm


class SupplierForm(BaseForm):
    """Formulaire pour ajouter/modifier un fournisseur."""

    # Signaux
    formSubmitted = pyqtSignal(dict)
    formCancelled = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()

    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)

        # En-tête du formulaire
        header_layout = QHBoxLayout()
        icon = IconWidget(FluentIcon.PEOPLE, self)
        icon.setFixedSize(24, 24)
        title = QLabel("Informations du fournisseur")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-left: 8px;")
        
        header_layout.addWidget(icon)
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)

        # Ligne de séparation
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #38BDF8; margin: 10px 0;")
        layout.addWidget(separator)

        # Formulaire principal
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # Code fournisseur
        self.codeEdit = QLineEdit()
        self.codeEdit.setPlaceholderText("Ex: FOUR001")
        form_layout.addRow("Code fournisseur *:", self.codeEdit)

        # Nom/Raison sociale
        self.nameEdit = QLineEdit()
        self.nameEdit.setPlaceholderText("Ex: TechnoPlus SARL")
        form_layout.addRow("Nom/Raison sociale *:", self.nameEdit)

        # Adresse
        self.addressEdit = QTextEdit()
        self.addressEdit.setPlaceholderText("Adresse complète du fournisseur")
        self.addressEdit.setMaximumHeight(80)
        form_layout.addRow("Adresse:", self.addressEdit)

        # Téléphone
        self.phoneEdit = QLineEdit()
        self.phoneEdit.setPlaceholderText("Ex: 021-123-456")
        form_layout.addRow("Téléphone:", self.phoneEdit)

        # Email
        self.emailEdit = QLineEdit()
        self.emailEdit.setPlaceholderText("Ex: <EMAIL>")
        form_layout.addRow("Email:", self.emailEdit)

        # Solde initial
        self.balanceEdit = QLineEdit()
        self.balanceEdit.setPlaceholderText("0.00")
        self.balanceEdit.setText("0.00")
        form_layout.addRow("Solde initial (DA):", self.balanceEdit)

        layout.addLayout(form_layout)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        self.cancelButton = QPushButton("Annuler")
        self.cancelButton.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.cancelButton.clicked.connect(self.cancel)

        self.saveButton = QPushButton("Enregistrer")
        self.saveButton.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #38BDF8, stop:1 #0ea5e9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                margin-left: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0ea5e9, stop:1 #38BDF8);
            }
        """)
        self.saveButton.clicked.connect(self.submit)

        buttons_layout.addWidget(self.cancelButton)
        buttons_layout.addWidget(self.saveButton)

        layout.addLayout(buttons_layout)

        # Style du formulaire
        self.setStyleSheet("""
            QLabel {
                color: #F1F5F9;
                font-size: 14px;
                font-weight: 500;
            }
            QLineEdit, QTextEdit {
                background-color: #23263A;
                border: 1.5px solid #334155;
                border-radius: 8px;
                padding: 8px 12px;
                color: #F1F5F9;
                font-size: 14px;
                selection-background-color: #38BDF8;
            }
            QLineEdit:focus, QTextEdit:focus {
                border: 1.5px solid #38BDF8;
                background-color: #1e293b;
            }
        """)

    def loadData(self, data):
        """Charge les données dans le formulaire."""
        self.codeEdit.setText(data.get("code", ""))
        self.nameEdit.setText(data.get("name", ""))
        self.addressEdit.setPlainText(data.get("address", ""))
        self.phoneEdit.setText(data.get("phone", ""))
        self.emailEdit.setText(data.get("email", ""))
        self.balanceEdit.setText(str(data.get("balance", "0.00")))

    def getData(self):
        """Récupère les données du formulaire."""
        return {
            "code": self.codeEdit.text().strip(),
            "name": self.nameEdit.text().strip(),
            "address": self.addressEdit.toPlainText().strip(),
            "phone": self.phoneEdit.text().strip(),
            "email": self.emailEdit.text().strip(),
            "balance": self.balanceEdit.text().strip() or "0.00"
        }

    def validate(self):
        """Valide les données du formulaire."""
        data = self.getData()
        
        if not data["code"]:
            return False, "Le code fournisseur est obligatoire."
        
        if not data["name"]:
            return False, "Le nom/raison sociale est obligatoire."
        
        # Validation du solde
        try:
            float(data["balance"])
        except ValueError:
            return False, "Le solde doit être un nombre valide."
        
        # Validation de l'email (basique)
        if data["email"] and "@" not in data["email"]:
            return False, "L'adresse email n'est pas valide."
        
        return True, ""

    def submit(self):
        """Soumet le formulaire."""
        is_valid, error_message = self.validate()
        
        if not is_valid:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Erreur de validation", error_message)
            return
        
        data = self.getData()
        self.formSubmitted.emit(data)

    def cancel(self):
        """Annule le formulaire."""
        self.formCancelled.emit()

    def clearForm(self):
        """Vide le formulaire."""
        self.codeEdit.clear()
        self.nameEdit.clear()
        self.addressEdit.clear()
        self.phoneEdit.clear()
        self.emailEdit.clear()
        self.balanceEdit.setText("0.00")
