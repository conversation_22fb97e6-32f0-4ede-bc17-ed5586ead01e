"""
Système de recherche globale pour GSCOM.
Recherche intelligente avec suggestions et filtres avancés.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit,
    QListWidget, QListWidgetItem, QLabel, QFrame,
    QPushButton, QComboBox, QScrollArea, QDialog
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPainter, QColor, QPixmap

from qfluentwidgets import (
    FluentIcon, IconWidget, LineEdit, ComboBox,
    PushButton, TransparentPushButton, CardWidget
)
from ui.themes.modern_themes import modern_theme_manager
import re
import difflib


class SearchResult:
    """Résultat de recherche."""
    
    def __init__(self, title, description, category, icon=FluentIcon.DOCUMENT, data=None):
        self.title = title
        self.description = description
        self.category = category
        self.icon = icon
        self.data = data
        self.relevance = 0.0


class SearchWorker(QThread):
    """Worker thread pour la recherche asynchrone."""
    
    resultsReady = pyqtSignal(list)
    
    def __init__(self, query, data_sources):
        super().__init__()
        self.query = query.lower()
        self.data_sources = data_sources
        
    def run(self):
        """Exécute la recherche."""
        results = []
        
        for source_name, source_data in self.data_sources.items():
            for item in source_data:
                relevance = self.calculate_relevance(item)
                if relevance > 0.1:  # Seuil de pertinence
                    result = SearchResult(
                        title=item.get('title', ''),
                        description=item.get('description', ''),
                        category=source_name,
                        icon=item.get('icon', FluentIcon.DOCUMENT),
                        data=item
                    )
                    result.relevance = relevance
                    results.append(result)
                    
        # Trier par pertinence
        results.sort(key=lambda x: x.relevance, reverse=True)
        
        self.resultsReady.emit(results[:50])  # Limiter à 50 résultats
        
    def calculate_relevance(self, item):
        """Calcule la pertinence d'un élément."""
        title = item.get('title', '').lower()
        description = item.get('description', '').lower()
        keywords = item.get('keywords', [])
        
        relevance = 0.0
        
        # Correspondance exacte dans le titre
        if self.query in title:
            relevance += 1.0
            
        # Correspondance exacte dans la description
        if self.query in description:
            relevance += 0.5
            
        # Correspondance dans les mots-clés
        for keyword in keywords:
            if self.query in keyword.lower():
                relevance += 0.3
                
        # Correspondance floue
        title_similarity = difflib.SequenceMatcher(None, self.query, title).ratio()
        if title_similarity > 0.6:
            relevance += title_similarity * 0.4
            
        return relevance


class SearchResultItem(QFrame):
    """Widget pour afficher un résultat de recherche."""
    
    clicked = pyqtSignal(SearchResult)
    
    def __init__(self, result: SearchResult, parent=None):
        super().__init__(parent)
        self.result = result
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface du résultat."""
        self.setFixedHeight(70)
        self.setCursor(Qt.PointingHandCursor)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(12)
        
        # Icône
        icon_widget = IconWidget(self.result.icon, self)
        icon_widget.setFixedSize(32, 32)
        
        # Contenu
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)
        
        # Titre
        title_label = QLabel(self.result.title)
        title_label.setObjectName("resultTitle")
        title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        # Description
        desc_label = QLabel(self.result.description)
        desc_label.setObjectName("resultDescription")
        desc_label.setFont(QFont("Segoe UI", 10))
        desc_label.setWordWrap(True)
        
        content_layout.addWidget(title_label)
        content_layout.addWidget(desc_label)
        
        # Catégorie
        category_label = QLabel(self.result.category)
        category_label.setObjectName("resultCategory")
        category_label.setFont(QFont("Segoe UI", 9))
        category_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        layout.addWidget(icon_widget)
        layout.addLayout(content_layout, 1)
        layout.addWidget(category_label)
        
        # Style
        self.update_style()
        
    def update_style(self):
        """Met à jour le style."""
        colors = modern_theme_manager.get_colors()
        
        self.setStyleSheet(f"""
            SearchResultItem {{
                background: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 8px;
            }}
            SearchResultItem:hover {{
                background: {colors['primary']}15;
                border: 1px solid {colors['primary']}40;
            }}
            QLabel#resultTitle {{
                color: {colors['text']};
            }}
            QLabel#resultDescription {{
                color: {colors['text_secondary']};
            }}
            QLabel#resultCategory {{
                color: {colors['primary']};
                background: {colors['primary']}20;
                border-radius: 4px;
                padding: 2px 8px;
            }}
        """)
        
    def mousePressEvent(self, event):
        """Gère le clic sur le résultat."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.result)
        super().mousePressEvent(event)


class GlobalSearchWidget(QWidget):
    """Widget de recherche globale."""
    
    resultSelected = pyqtSignal(SearchResult)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_worker = None
        self.data_sources = self.initialize_data_sources()
        self.setupUI()
        
        # Timer pour la recherche différée
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        
    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # Barre de recherche
        search_layout = QHBoxLayout()
        
        # Champ de recherche
        self.search_input = LineEdit()
        self.search_input.setPlaceholderText("Rechercher dans GSCOM...")
        self.search_input.setClearButtonEnabled(True)
        self.search_input.textChanged.connect(self.on_search_text_changed)
        self.search_input.setMinimumHeight(40)
        
        # Filtre de catégorie
        self.category_filter = ComboBox()
        self.category_filter.addItems([
            "Toutes les catégories",
            "Produits",
            "Clients",
            "Fournisseurs",
            "Ventes",
            "Commandes",
            "Rapports"
        ])
        self.category_filter.currentTextChanged.connect(self.on_filter_changed)
        
        search_layout.addWidget(self.search_input, 1)
        search_layout.addWidget(self.category_filter)
        
        # Zone de résultats
        self.results_area = QScrollArea()
        self.results_area.setWidgetResizable(True)
        self.results_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.results_widget = QWidget()
        self.results_layout = QVBoxLayout(self.results_widget)
        self.results_layout.setSpacing(5)
        
        self.results_area.setWidget(self.results_widget)
        
        # Message par défaut
        self.default_message = QLabel("Tapez pour rechercher...")
        self.default_message.setAlignment(Qt.AlignCenter)
        self.default_message.setStyleSheet("color: #6B7280; font-size: 14px; padding: 40px;")
        
        layout.addLayout(search_layout)
        layout.addWidget(self.default_message)
        layout.addWidget(self.results_area)
        
        # Masquer les résultats initialement
        self.results_area.hide()
        
    def initialize_data_sources(self):
        """Initialise les sources de données pour la recherche."""
        return {
            "Produits": [
                {
                    "title": "Ordinateur Portable HP",
                    "description": "Laptop HP Pavilion 15.6\" Intel Core i5",
                    "keywords": ["ordinateur", "laptop", "hp", "pavilion", "intel"],
                    "icon": FluentIcon.LAPTOP
                },
                {
                    "title": "Smartphone Samsung Galaxy",
                    "description": "Samsung Galaxy S21 128GB Noir",
                    "keywords": ["smartphone", "samsung", "galaxy", "téléphone"],
                    "icon": FluentIcon.PHONE
                },
                {
                    "title": "Imprimante Canon",
                    "description": "Imprimante multifonction Canon PIXMA",
                    "keywords": ["imprimante", "canon", "pixma", "multifonction"],
                    "icon": FluentIcon.PRINT
                }
            ],
            "Clients": [
                {
                    "title": "TechCorp SARL",
                    "description": "Entreprise de technologie - Contact: Ahmed Benali",
                    "keywords": ["techcorp", "entreprise", "technologie", "ahmed", "benali"],
                    "icon": FluentIcon.PEOPLE
                },
                {
                    "title": "Digital Solutions",
                    "description": "Société de services numériques - Contact: Fatima Zohra",
                    "keywords": ["digital", "solutions", "services", "fatima", "zohra"],
                    "icon": FluentIcon.PEOPLE
                }
            ],
            "Fournisseurs": [
                {
                    "title": "ElectroMag Distribution",
                    "description": "Fournisseur d'équipements électroniques",
                    "keywords": ["electromag", "distribution", "électronique", "fournisseur"],
                    "icon": FluentIcon.MARKET
                },
                {
                    "title": "InfoSys Wholesale",
                    "description": "Grossiste en matériel informatique",
                    "keywords": ["infosys", "wholesale", "grossiste", "informatique"],
                    "icon": FluentIcon.MARKET
                }
            ],
            "Ventes": [
                {
                    "title": "Vente #VT-2024-001",
                    "description": "Vente à TechCorp - 15 450 DA",
                    "keywords": ["vente", "techcorp", "facture"],
                    "icon": FluentIcon.SHOPPING_CART
                },
                {
                    "title": "Vente #VT-2024-002",
                    "description": "Vente à Digital Solutions - 28 750 DA",
                    "keywords": ["vente", "digital", "solutions"],
                    "icon": FluentIcon.SHOPPING_CART
                }
            ],
            "Rapports": [
                {
                    "title": "Rapport de Ventes Mensuel",
                    "description": "Rapport des ventes pour le mois en cours",
                    "keywords": ["rapport", "ventes", "mensuel", "statistiques"],
                    "icon": FluentIcon.DOCUMENT
                },
                {
                    "title": "Rapport d'Inventaire",
                    "description": "État actuel du stock et mouvements",
                    "keywords": ["rapport", "inventaire", "stock", "mouvements"],
                    "icon": FluentIcon.DOCUMENT
                }
            ]
        }
        
    def on_search_text_changed(self, text):
        """Appelé quand le texte de recherche change."""
        if len(text) >= 2:
            # Démarrer la recherche avec un délai
            self.search_timer.start(300)  # 300ms de délai
        else:
            self.clear_results()
            
    def on_filter_changed(self, category):
        """Appelé quand le filtre de catégorie change."""
        if self.search_input.text():
            self.perform_search()
            
    def perform_search(self):
        """Effectue la recherche."""
        query = self.search_input.text().strip()
        if len(query) < 2:
            return
            
        # Filtrer les sources de données selon la catégorie
        category_filter = self.category_filter.currentText()
        if category_filter == "Toutes les catégories":
            filtered_sources = self.data_sources
        else:
            # Mapper les noms de filtre aux clés de données
            category_map = {
                "Produits": "Produits",
                "Clients": "Clients", 
                "Fournisseurs": "Fournisseurs",
                "Ventes": "Ventes",
                "Commandes": "Ventes",  # Même source que ventes
                "Rapports": "Rapports"
            }
            key = category_map.get(category_filter, category_filter)
            filtered_sources = {key: self.data_sources.get(key, [])}
            
        # Arrêter le worker précédent s'il existe
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.terminate()
            self.search_worker.wait()
            
        # Démarrer la nouvelle recherche
        self.search_worker = SearchWorker(query, filtered_sources)
        self.search_worker.resultsReady.connect(self.display_results)
        self.search_worker.start()
        
    def display_results(self, results):
        """Affiche les résultats de recherche."""
        # Nettoyer les résultats précédents
        self.clear_results()
        
        if not results:
            # Aucun résultat
            no_results = QLabel("Aucun résultat trouvé")
            no_results.setAlignment(Qt.AlignCenter)
            no_results.setStyleSheet("color: #6B7280; font-size: 14px; padding: 20px;")
            self.results_layout.addWidget(no_results)
        else:
            # Afficher les résultats
            for result in results:
                result_item = SearchResultItem(result)
                result_item.clicked.connect(self.on_result_clicked)
                self.results_layout.addWidget(result_item)
                
        # Afficher la zone de résultats
        self.default_message.hide()
        self.results_area.show()
        
    def clear_results(self):
        """Efface les résultats de recherche."""
        # Supprimer tous les widgets de résultats
        while self.results_layout.count():
            child = self.results_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
        # Afficher le message par défaut
        self.results_area.hide()
        self.default_message.show()
        
    def on_result_clicked(self, result):
        """Appelé quand un résultat est cliqué."""
        self.resultSelected.emit(result)


class GlobalSearchDialog(QDialog):
    """Dialog de recherche globale."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface du dialog."""
        self.setWindowTitle("Recherche Globale - GSCOM")
        self.setFixedSize(600, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Widget de recherche
        self.search_widget = GlobalSearchWidget()
        self.search_widget.resultSelected.connect(self.on_result_selected)
        
        layout.addWidget(self.search_widget)
        
        # Bouton de fermeture
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        
        close_btn = PushButton("Fermer")
        close_btn.clicked.connect(self.close)
        close_layout.addWidget(close_btn)
        
        layout.addLayout(close_layout)
        
        # Style
        colors = modern_theme_manager.get_colors()
        self.setStyleSheet(f"""
            GlobalSearchDialog {{
                background: {colors['background']};
            }}
        """)
        
    def on_result_selected(self, result):
        """Appelé quand un résultat est sélectionné."""
        from ui.components.modern_notifications import show_info
        show_info(
            "Résultat sélectionné",
            f"Ouverture de: {result.title}"
        )
        # Ici, vous pouvez ajouter la logique pour naviguer vers l'élément sélectionné
        self.close()
