"""
Plugin d'exemple pour GSCOM.
"""
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton
from core.plugin_system import UIPluginInterface


class SamplePlugin(UIPluginInterface):
    """Plugin d'exemple."""
    
    @property
    def name(self) -> str:
        return "Plugin d'Exemple"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Un plugin d'exemple pour démontrer le système"
    
    @property
    def author(self) -> str:
        return "GSCOM Team"
    
    def initialize(self, app_context):
        """Initialise le plugin."""
        print(f"Plugin {self.name} initialisé")
        return True
    
    def cleanup(self):
        """Nettoie les ressources."""
        print(f"Plugin {self.name} nettoyé")
    
    def create_widget(self, parent=None):
        """Crée le widget du plugin."""
        widget = QWidget(parent)
        layout = QVBoxLayout(widget)
        
        label = QLabel("Ceci est un plugin d'exemple")
        button = QPushButton("Bouton du plugin")
        
        layout.addWidget(label)
        layout.addWidget(button)
        
        return widget
    
    def get_category(self):
        return "Exemple"
