"""
Composants de graphiques modernes pour GSCOM.
Graphiques interactifs avec animations et design moderne.
"""
import random
import math
from datetime import datetime, timedelta
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QLinearGradient, QFont, QPolygonF
from PyQt5.QtCore import QPointF, QRectF

from qfluentwidgets import FluentIcon, IconWidget
from ui.themes.modern_themes import modern_theme_manager


class AnimatedChart(QWidget):
    """Graphique animé de base."""

    def __init__(self, title="Graphique", parent=None):
        super().__init__(parent)
        self.title = title
        self.data_points = []
        self.max_points = 30
        self._animation_progress = 0.0
        self.setupUI()
        self.setupAnimation()

    def setupUI(self):
        """Configure l'interface du graphique."""
        self.setMinimumSize(400, 250)

        # Timer pour animation
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)

    def setupAnimation(self):
        """Configure les animations."""
        self.animation = QPropertyAnimation(self, b"animation_progress")
        self.animation.setDuration(1000)
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)

    def add_data_point(self, value):
        """Ajoute un point de données."""
        self.data_points.append(value)
        if len(self.data_points) > self.max_points:
            self.data_points.pop(0)
        self.start_animation()

    def start_animation(self):
        """Démarre l'animation."""
        self.animation.start()

    def update_animation(self):
        """Met à jour l'animation."""
        self.update()

    def get_animation_progress(self):
        """Retourne le progrès de l'animation."""
        return self._animation_progress

    def set_animation_progress(self, value):
        """Définit le progrès de l'animation."""
        self._animation_progress = value
        self.update()

    animation_progress = property(get_animation_progress, set_animation_progress)


class SalesChart(AnimatedChart):
    """Graphique des ventes avec courbe lissée."""

    def __init__(self, parent=None):
        super().__init__("Évolution des Ventes", parent)
        self.generate_sample_data()

    def generate_sample_data(self):
        """Génère des données d'exemple."""
        base_value = 100000
        for i in range(self.max_points):
            # Simulation d'une tendance avec variations
            trend = i * 1000
            variation = random.randint(-15000, 20000)
            seasonal = math.sin(i * 0.3) * 10000
            value = max(0, base_value + trend + variation + seasonal)
            self.data_points.append(value)

    def paintEvent(self, event):
        """Dessine le graphique des ventes."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Obtenir les couleurs du thème
        colors = modern_theme_manager.get_colors()

        # Fond du graphique
        rect = self.rect()
        painter.fillRect(rect, QColor(colors['surface']))

        # Bordure
        painter.setPen(QPen(QColor(colors['border']), 1))
        painter.drawRect(rect)

        # Titre
        painter.setPen(QColor(colors['text']))
        painter.setFont(QFont("Segoe UI", 12, QFont.Bold))
        title_rect = QRectF(10, 10, rect.width() - 20, 30)
        painter.drawText(title_rect, Qt.AlignLeft | Qt.AlignVCenter, self.title)

        # Zone de dessin du graphique
        chart_rect = QRectF(40, 50, rect.width() - 80, rect.height() - 100)

        if not self.data_points:
            return

        # Calculer les valeurs min/max
        min_val = min(self.data_points)
        max_val = max(self.data_points)
        val_range = max_val - min_val if max_val != min_val else 1

        # Dessiner la grille
        self.draw_grid(painter, chart_rect, colors)

        # Dessiner la courbe
        self.draw_curve(painter, chart_rect, min_val, val_range, colors)

        # Dessiner les axes
        self.draw_axes(painter, chart_rect, min_val, max_val, colors)

    def draw_grid(self, painter, rect, colors):
        """Dessine la grille du graphique."""
        painter.setPen(QPen(QColor(colors['border']), 1, Qt.DotLine))

        # Lignes horizontales
        for i in range(5):
            y = rect.top() + (rect.height() / 4) * i
            painter.drawLine(rect.left(), y, rect.right(), y)

        # Lignes verticales
        for i in range(6):
            x = rect.left() + (rect.width() / 5) * i
            painter.drawLine(x, rect.top(), x, rect.bottom())

    def draw_curve(self, painter, rect, min_val, val_range, colors):
        """Dessine la courbe des ventes."""
        if len(self.data_points) < 2:
            return

        # Créer le gradient pour la zone sous la courbe
        gradient = QLinearGradient(0, rect.top(), 0, rect.bottom())
        gradient.setColorAt(0, QColor(colors['primary'] + "80"))
        gradient.setColorAt(1, QColor(colors['primary'] + "20"))

        # Points de la courbe
        points = []
        animated_count = int(len(self.data_points) * self.animation_progress)

        for i in range(min(animated_count, len(self.data_points))):
            x = rect.left() + (rect.width() / (len(self.data_points) - 1)) * i
            y_ratio = (self.data_points[i] - min_val) / val_range
            y = rect.bottom() - (rect.height() * y_ratio)
            points.append(QPointF(x, y))

        if len(points) < 2:
            return

        # Dessiner la zone sous la courbe
        area_points = [QPointF(rect.left(), rect.bottom())] + points + [QPointF(points[-1].x(), rect.bottom())]
        area_polygon = QPolygonF(area_points)
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        painter.drawPolygon(area_polygon)

        # Dessiner la ligne de la courbe
        painter.setBrush(Qt.NoBrush)
        painter.setPen(QPen(QColor(colors['primary']), 3))
        curve_polygon = QPolygonF(points)
        painter.drawPolyline(curve_polygon)

        # Dessiner les points
        painter.setBrush(QBrush(QColor(colors['primary'])))
        painter.setPen(QPen(QColor(colors['background']), 2))
        for point in points[-5:]:  # Seulement les 5 derniers points
            painter.drawEllipse(point, 4, 4)

    def draw_axes(self, painter, rect, min_val, max_val, colors):
        """Dessine les axes et les labels."""
        painter.setPen(QColor(colors['text_secondary']))
        painter.setFont(QFont("Segoe UI", 9))

        # Labels de l'axe Y (valeurs)
        for i in range(5):
            y = rect.top() + (rect.height() / 4) * i
            value = max_val - ((max_val - min_val) / 4) * i
            label = f"{value/1000:.0f}K DA"
            painter.drawText(QRectF(5, y - 10, 35, 20), Qt.AlignRight | Qt.AlignVCenter, label)

        # Labels de l'axe X (temps)
        for i in range(6):
            x = rect.left() + (rect.width() / 5) * i
            days_ago = 25 - (i * 5)
            date = datetime.now() - timedelta(days=days_ago)
            label = date.strftime("%d/%m")
            painter.drawText(QRectF(x - 20, rect.bottom() + 5, 40, 20), Qt.AlignCenter, label)


class DonutChart(AnimatedChart):
    """Graphique en donut pour les catégories."""

    def __init__(self, title="Répartition", parent=None):
        super().__init__(title, parent)
        self.segments = [
            {"label": "Électronique", "value": 35, "color": "#3B82F6"},
            {"label": "Informatique", "value": 28, "color": "#10B981"},
            {"label": "Accessoires", "value": 20, "color": "#F59E0B"},
            {"label": "Autres", "value": 17, "color": "#EF4444"}
        ]

    def paintEvent(self, event):
        """Dessine le graphique en donut."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Obtenir les couleurs du thème
        colors = modern_theme_manager.get_colors()

        # Fond
        rect = self.rect()
        painter.fillRect(rect, QColor(colors['surface']))

        # Bordure
        painter.setPen(QPen(QColor(colors['border']), 1))
        painter.drawRect(rect)

        # Titre
        painter.setPen(QColor(colors['text']))
        painter.setFont(QFont("Segoe UI", 12, QFont.Bold))
        title_rect = QRectF(10, 10, rect.width() - 20, 30)
        painter.drawText(title_rect, Qt.AlignCenter, self.title)

        # Zone du donut
        center_x = rect.width() // 2
        center_y = rect.height() // 2 + 10
        outer_radius = min(rect.width(), rect.height() - 80) // 3
        inner_radius = outer_radius // 2

        # Dessiner les segments
        start_angle = 0
        total_value = sum(segment["value"] for segment in self.segments)

        for i, segment in enumerate(self.segments):
            # Calculer l'angle du segment
            segment_angle = (segment["value"] / total_value) * 360 * self.animation_progress

            # Dessiner le segment
            painter.setBrush(QBrush(QColor(segment["color"])))
            painter.setPen(QPen(QColor(colors['background']), 2))

            # Rectangle englobant pour l'arc
            arc_rect = QRectF(
                center_x - outer_radius,
                center_y - outer_radius,
                outer_radius * 2,
                outer_radius * 2
            )

            # Dessiner l'arc externe
            painter.drawPie(arc_rect, start_angle * 16, segment_angle * 16)

            # Dessiner le trou central
            inner_rect = QRectF(
                center_x - inner_radius,
                center_y - inner_radius,
                inner_radius * 2,
                inner_radius * 2
            )
            painter.setBrush(QBrush(QColor(colors['surface'])))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(inner_rect)

            start_angle += segment_angle

        # Légende
        self.draw_legend(painter, rect, colors)

    def draw_legend(self, painter, rect, colors):
        """Dessine la légende."""
        painter.setFont(QFont("Segoe UI", 9))

        legend_x = rect.width() - 120
        legend_y = 60

        for i, segment in enumerate(self.segments):
            y = legend_y + i * 25

            # Carré de couleur
            color_rect = QRectF(legend_x, y, 12, 12)
            painter.setBrush(QBrush(QColor(segment["color"])))
            painter.setPen(Qt.NoPen)
            painter.drawRect(color_rect)

            # Texte
            painter.setPen(QColor(colors['text']))
            text_rect = QRectF(legend_x + 20, y - 2, 100, 16)
            text = f"{segment['label']} ({segment['value']}%)"
            painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, text)


class BarChart(AnimatedChart):
    """Graphique en barres pour les comparaisons."""

    def __init__(self, title="Comparaison", parent=None):
        super().__init__(title, parent)
        self.bars = [
            {"label": "Jan", "value": 85000},
            {"label": "Fév", "value": 92000},
            {"label": "Mar", "value": 78000},
            {"label": "Avr", "value": 105000},
            {"label": "Mai", "value": 118000},
            {"label": "Jun", "value": 95000}
        ]

    def paintEvent(self, event):
        """Dessine le graphique en barres."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Obtenir les couleurs du thème
        colors = modern_theme_manager.get_colors()

        # Fond
        rect = self.rect()
        painter.fillRect(rect, QColor(colors['surface']))

        # Bordure
        painter.setPen(QPen(QColor(colors['border']), 1))
        painter.drawRect(rect)

        # Titre
        painter.setPen(QColor(colors['text']))
        painter.setFont(QFont("Segoe UI", 12, QFont.Bold))
        title_rect = QRectF(10, 10, rect.width() - 20, 30)
        painter.drawText(title_rect, Qt.AlignCenter, self.title)

        # Zone de dessin
        chart_rect = QRectF(40, 50, rect.width() - 80, rect.height() - 100)

        if not self.bars:
            return

        # Valeur maximale
        max_value = max(bar["value"] for bar in self.bars)

        # Largeur des barres
        bar_width = chart_rect.width() / len(self.bars) * 0.7
        bar_spacing = chart_rect.width() / len(self.bars)

        # Dessiner les barres
        for i, bar in enumerate(self.bars):
            # Position de la barre
            x = chart_rect.left() + i * bar_spacing + (bar_spacing - bar_width) / 2

            # Hauteur animée
            height_ratio = (bar["value"] / max_value) * self.animation_progress
            height = chart_rect.height() * height_ratio
            y = chart_rect.bottom() - height

            # Gradient pour la barre
            gradient = QLinearGradient(0, y, 0, chart_rect.bottom())
            gradient.setColorAt(0, QColor(colors['primary']))
            gradient.setColorAt(1, QColor(colors['secondary']))

            # Dessiner la barre
            bar_rect = QRectF(x, y, bar_width, height)
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(bar_rect, 4, 4)

            # Label de la barre
            painter.setPen(QColor(colors['text_secondary']))
            painter.setFont(QFont("Segoe UI", 9))
            label_rect = QRectF(x, chart_rect.bottom() + 5, bar_width, 20)
            painter.drawText(label_rect, Qt.AlignCenter, bar["label"])

            # Valeur au-dessus de la barre
            if height > 20:  # Seulement si la barre est assez haute
                painter.setPen(QColor(colors['text']))
                value_text = f"{bar['value']/1000:.0f}K"
                value_rect = QRectF(x, y - 20, bar_width, 15)
                painter.drawText(value_rect, Qt.AlignCenter, value_text)
