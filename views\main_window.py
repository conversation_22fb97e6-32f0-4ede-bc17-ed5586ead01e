"""
Fenêtre principale futuriste pour l'application SGest.
Interface cyberpunk avec effets holographiques et néons.
"""
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtWidgets import QApplication
from qfluentwidgets import FluentWindow, FluentIcon
from views.products import ProductsWidget
from views.dashboard import FuturisticDashboardWidget
from views.inventory import InventoryWidget
from views.reports import ReportsWidget
from views.futuristic_settings import FuturisticSettingsWidget
from ui.styles.futuristic_theme import get_futuristic_style, apply_glow_effect, FuturisticAnimations
from ui.components.particle_system import ParticleSystem, HolographicGrid
from utils.notifications import get_notification_manager, show_success
from utils.futuristic_sounds import get_sound_manager, play_startup_sound


class MainWindow(FluentWindow):
    """Fenêtre principale de l'application SGest."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupWidgets()
        self.setupNavigation()
        self.setupWindow()

    def setupWidgets(self):
        """Configure les widgets de la fenêtre principale."""
        # Widget du tableau de bord futuriste
        self.dashboard = FuturisticDashboardWidget(self)
        self.dashboard.setObjectName("dashboardWidget")

        # Widget des produits
        self.products = ProductsWidget(self)
        self.products.setObjectName("productsWidget")

        # Widget de gestion des stocks
        self.inventory = InventoryWidget(self)
        self.inventory.setObjectName("inventoryWidget")

        # Widget des rapports
        self.reports = ReportsWidget(self)
        self.reports.setObjectName("reportsWidget")

        # Widget des paramètres futuristes
        self.futuristic_settings = FuturisticSettingsWidget(self)
        self.futuristic_settings.setObjectName("futuristicSettingsWidget")

        # Ajouter les widgets au stacked widget
        self.addSubInterface(self.dashboard, FluentIcon.HOME, "Tableau de bord")
        self.addSubInterface(self.products, FluentIcon.SHOPPING_CART, "Produits")
        self.addSubInterface(self.inventory, FluentIcon.FOLDER, "Stocks")
        self.addSubInterface(self.reports, FluentIcon.DOCUMENT, "Rapports")
        self.addSubInterface(self.futuristic_settings, FluentIcon.SETTING, "Paramètres FX")

    def setupNavigation(self):
        """Configure la navigation."""
        # La navigation est automatiquement configurée par addSubInterface
        pass

    def setupWindow(self):
        """Configure la fenêtre principale futuriste."""
        self.setWindowTitle("SGest - Système de Gestion Futuriste")
        self.resize(1600, 1000)

        # Appliquer le thème futuriste
        self.setStyleSheet(get_futuristic_style())

        # Définir le tableau de bord comme vue par défaut
        self.stackedWidget.setCurrentWidget(self.dashboard)

        # Connecter les signaux entre les widgets
        self.connectSignals()

        # Configurer les effets visuels
        self.setupVisualEffects()

        # Son de démarrage futuriste
        try:
            play_startup_sound()
        except Exception as e:
            print(f"AVERTISSEMENT: Impossible de jouer le son de démarrage: {e}")

        # Notification de bienvenue
        try:
            show_success(
                "Système initialisé",
                "Interface futuriste SGest activée avec succès",
                parent=self
            )
        except Exception as e:
            print(f"AVERTISSEMENT: Impossible d'afficher la notification: {e}")

    def setupVisualEffects(self):
        """Configure les effets visuels futuristes."""
        # Appliquer des effets de lueur aux éléments principaux
        apply_glow_effect(self.navigationInterface, "#00FFFF", 10)

        # Animation d'entrée pour les widgets
        FuturisticAnimations.slide_in_neon(self.dashboard, "left", 1000)

        # Timer pour les effets périodiques
        self.effects_timer = QTimer()
        self.effects_timer.timeout.connect(self.updateVisualEffects)
        self.effects_timer.start(5000)  # Toutes les 5 secondes

    def updateVisualEffects(self):
        """Met à jour les effets visuels périodiquement."""
        # Déclencher des effets de particules aléatoires
        if hasattr(self.dashboard, 'triggerParticleExplosion'):
            import random
            if random.random() < 0.3:  # 30% de chance
                self.dashboard.triggerParticleExplosion()

    def connectSignals(self):
        """Connecte les signaux entre les différents widgets."""
        # Connecter les signaux du tableau de bord
        self.dashboard.refreshRequested.connect(self.refreshAllData)

        # Connecter les signaux de l'inventaire
        self.inventory.stockUpdated.connect(self.onStockUpdated)
        self.inventory.movementAdded.connect(self.onMovementAdded)

        # Connecter les signaux des rapports
        self.reports.reportGenerated.connect(self.onReportGenerated)

        # Connecter les signaux des paramètres futuristes
        if hasattr(self.futuristic_settings, 'main_control_panel'):
            self.futuristic_settings.main_control_panel.particleRateChanged.connect(self.onParticleRateChanged)
            self.futuristic_settings.main_control_panel.matrixToggled.connect(self.onMatrixToggled)

    def refreshAllData(self):
        """Actualise toutes les données de l'application."""
        # Actualiser les données de tous les widgets
        if hasattr(self.products, 'refresh_data'):
            self.products.refresh_data()
        if hasattr(self.inventory, 'loadSampleData'):
            self.inventory.loadSampleData()
        if hasattr(self.reports, 'loadSampleData'):
            self.reports.loadSampleData()

    def onStockUpdated(self, product_name, new_quantity):
        """Gère la mise à jour du stock."""
        print(f"Stock mis à jour: {product_name} -> {new_quantity}")

    def onMovementAdded(self, movement_data):
        """Gère l'ajout d'un mouvement de stock."""
        print(f"Mouvement ajouté: {movement_data}")

    def onReportGenerated(self, report_type, data):
        """Gère la génération d'un rapport."""
        print(f"Rapport généré: {report_type} -> {data}")

    def onParticleRateChanged(self, rate):
        """Gère le changement de taux de particules."""
        print(f"Taux de particules changé: {rate}")
        # Appliquer le changement au tableau de bord
        if hasattr(self.dashboard, 'setParticleEmissionRate'):
            self.dashboard.setParticleEmissionRate(rate)

    def onMatrixToggled(self, enabled):
        """Gère l'activation/désactivation de l'effet Matrix."""
        print(f"Effet Matrix: {'Activé' if enabled else 'Désactivé'}")
        # Appliquer le changement au tableau de bord
        if hasattr(self.dashboard, 'toggleMatrixEffect'):
            self.dashboard.toggleMatrixEffect()

        # Notification
        status = "activé" if enabled else "désactivé"
        show_success(
            "Effet Matrix",
            f"L'effet Matrix a été {status}",
            parent=self
        )