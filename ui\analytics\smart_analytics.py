"""
Module d'analytics intelligent pour GSCOM.
Analyse prédictive et insights automatiques avec IA.
"""
import random
import math
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QTabWidget, QScrollArea,
    QProgressBar, QTextEdit
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPainter, QColor, QLinearGradient, QBrush

from qfluentwidgets import FluentIcon, IconWidget, PushButton, CardWidget, InfoBar, InfoBarPosition
from ui.themes.modern_themes import modern_theme_manager
from ui.components.charts import AnimatedChart
import json


class AIInsight:
    """Insight généré par l'IA."""

    def __init__(self, title, description, confidence, category, action_items=None):
        self.title = title
        self.description = description
        self.confidence = confidence  # 0.0 à 1.0
        self.category = category
        self.action_items = action_items or []
        self.timestamp = datetime.now()
        self.priority = self.calculate_priority()

    def calculate_priority(self):
        """Calcule la priorité basée sur la confiance et la catégorie."""
        category_weights = {
            "Revenue": 1.0,
            "Risk": 0.9,
            "Opportunity": 0.8,
            "Efficiency": 0.7,
            "Customer": 0.6
        }
        weight = category_weights.get(self.category, 0.5)
        return self.confidence * weight


class PredictiveAnalyzer(QThread):
    """Analyseur prédictif utilisant des algorithmes d'IA simulés."""

    insightGenerated = pyqtSignal(AIInsight)
    analysisComplete = pyqtSignal(list)

    def __init__(self, data_sources):
        super().__init__()
        self.data_sources = data_sources

    def run(self):
        """Exécute l'analyse prédictive."""
        insights = []

        # Simuler différents types d'analyses
        insights.extend(self.analyze_sales_trends())
        insights.extend(self.analyze_customer_behavior())
        insights.extend(self.analyze_inventory_risks())
        insights.extend(self.analyze_revenue_opportunities())

        # Trier par priorité
        insights.sort(key=lambda x: x.priority, reverse=True)

        # Émettre les insights un par un avec délai
        for insight in insights:
            self.insightGenerated.emit(insight)
            self.msleep(500)  # Délai pour effet visuel

        self.analysisComplete.emit(insights)

    def analyze_sales_trends(self):
        """Analyse les tendances de vente."""
        insights = []

        # Simuler une analyse de tendance
        trend_direction = random.choice(["up", "down", "stable"])
        confidence = random.uniform(0.7, 0.95)

        if trend_direction == "up":
            insight = AIInsight(
                "Tendance de vente positive détectée",
                "Les ventes montrent une croissance de 15% sur les 30 derniers jours. "
                "Cette tendance devrait se maintenir selon les modèles prédictifs.",
                confidence,
                "Revenue",
                ["Augmenter le stock des produits populaires", "Planifier une campagne marketing"]
            )
        elif trend_direction == "down":
            insight = AIInsight(
                "Baisse des ventes identifiée",
                "Une diminution de 8% des ventes est observée. "
                "Analyse recommandée des facteurs externes et de la concurrence.",
                confidence,
                "Risk",
                ["Analyser la concurrence", "Réviser la stratégie prix", "Contacter les clients inactifs"]
            )
        else:
            insight = AIInsight(
                "Ventes stables avec potentiel d'optimisation",
                "Les ventes restent stables mais des opportunités d'amélioration existent.",
                confidence,
                "Efficiency",
                ["Optimiser les processus de vente", "Former l'équipe commerciale"]
            )

        insights.append(insight)
        return insights

    def analyze_customer_behavior(self):
        """Analyse le comportement client."""
        insights = []

        # Simuler une analyse de segmentation client
        segments = [
            ("Clients VIP", "20% des clients génèrent 60% du chiffre d'affaires"),
            ("Clients à risque", "15% des clients n'ont pas acheté depuis 3 mois"),
            ("Nouveaux clients", "25% d'augmentation des nouveaux clients ce mois")
        ]

        for segment, description in segments:
            confidence = random.uniform(0.8, 0.95)
            insight = AIInsight(
                f"Segment identifié: {segment}",
                description,
                confidence,
                "Customer",
                ["Créer une campagne ciblée", "Personnaliser l'offre"]
            )
            insights.append(insight)

        return insights

    def analyze_inventory_risks(self):
        """Analyse les risques d'inventaire."""
        insights = []

        # Simuler une analyse de stock
        risk_items = [
            ("Rupture de stock imminente", "3 produits risquent la rupture dans 7 jours"),
            ("Surstockage détecté", "5 produits ont un stock excessif (>90 jours)"),
            ("Rotation lente", "8 produits ont une rotation inférieure à la moyenne")
        ]

        for title, description in risk_items:
            confidence = random.uniform(0.85, 0.98)
            insight = AIInsight(
                title,
                description,
                confidence,
                "Risk",
                ["Réapprovisionner en urgence", "Créer une promotion", "Analyser la demande"]
            )
            insights.append(insight)

        return insights

    def analyze_revenue_opportunities(self):
        """Analyse les opportunités de revenus."""
        insights = []

        opportunities = [
            ("Cross-selling potentiel", "40% des clients achètent un seul type de produit"),
            ("Upselling identifié", "Opportunité d'augmenter le panier moyen de 25%"),
            ("Saisonnalité favorable", "Période optimale pour lancer de nouveaux produits")
        ]

        for title, description in opportunities:
            confidence = random.uniform(0.75, 0.90)
            insight = AIInsight(
                title,
                description,
                confidence,
                "Opportunity",
                ["Créer des bundles", "Former l'équipe vente", "Lancer une campagne"]
            )
            insights.append(insight)

        return insights


class InsightCard(CardWidget):
    """Carte d'affichage d'un insight IA."""

    actionRequested = pyqtSignal(AIInsight)

    def __init__(self, insight: AIInsight, parent=None):
        super().__init__(parent)
        self.insight = insight
        self.setupUI()

    def setupUI(self):
        """Configure l'interface de la carte."""
        self.setFixedHeight(180)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # En-tête avec catégorie et confiance
        header_layout = QHBoxLayout()

        # Icône de catégorie
        category_icons = {
            "Revenue": FluentIcon.MONEY,
            "Risk": FluentIcon.CARE_RIGHT_SOLID,
            "Opportunity": FluentIcon.EMOJI_TAB_SYMBOLS,
            "Efficiency": FluentIcon.SPEED_HIGH,
            "Customer": FluentIcon.PEOPLE
        }

        icon = IconWidget(category_icons.get(self.insight.category, FluentIcon.INFO), self)
        icon.setFixedSize(24, 24)

        # Catégorie
        category_label = QLabel(self.insight.category)
        category_label.setObjectName("insightCategory")

        # Confiance
        confidence_label = QLabel(f"{self.insight.confidence*100:.0f}% confiance")
        confidence_label.setObjectName("insightConfidence")

        header_layout.addWidget(icon)
        header_layout.addWidget(category_label)
        header_layout.addStretch()
        header_layout.addWidget(confidence_label)

        # Titre
        title_label = QLabel(self.insight.title)
        title_label.setObjectName("insightTitle")
        title_label.setWordWrap(True)
        title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))

        # Description
        desc_label = QLabel(self.insight.description)
        desc_label.setObjectName("insightDescription")
        desc_label.setWordWrap(True)
        desc_label.setFont(QFont("Segoe UI", 10))

        # Actions
        if self.insight.action_items:
            actions_layout = QHBoxLayout()

            action_btn = PushButton("Voir Actions")
            action_btn.setIcon(FluentIcon.PLAY.icon())
            action_btn.clicked.connect(lambda: self.actionRequested.emit(self.insight))

            actions_layout.addWidget(action_btn)
            actions_layout.addStretch()

            layout.addLayout(header_layout)
            layout.addWidget(title_label)
            layout.addWidget(desc_label)
            layout.addLayout(actions_layout)
        else:
            layout.addLayout(header_layout)
            layout.addWidget(title_label)
            layout.addWidget(desc_label)

        # Style selon la priorité
        self.apply_priority_style()

    def apply_priority_style(self):
        """Applique le style selon la priorité."""
        colors = modern_theme_manager.get_colors()

        if self.insight.priority > 0.8:
            border_color = "#EF4444"  # Rouge pour haute priorité
        elif self.insight.priority > 0.6:
            border_color = "#F59E0B"  # Orange pour moyenne priorité
        else:
            border_color = colors['primary']  # Couleur normale

        self.setStyleSheet(f"""
            InsightCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {border_color}15,
                    stop:1 {border_color}08);
                border: 1px solid {border_color}40;
                border-left: 4px solid {border_color};
                border-radius: 12px;
            }}
            QLabel#insightCategory {{
                color: {border_color};
                font-weight: bold;
                font-size: 11px;
            }}
            QLabel#insightConfidence {{
                color: {colors['text_secondary']};
                font-size: 10px;
            }}
            QLabel#insightTitle {{
                color: {colors['text']};
            }}
            QLabel#insightDescription {{
                color: {colors['text_secondary']};
            }}
        """)


class SmartAnalyticsDashboard(QWidget):
    """Tableau de bord d'analytics intelligent."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.insights = []
        self.analyzer = None
        self.setupUI()
        self.setupTimers()

    def setupUI(self):
        """Configure l'interface utilisateur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête
        header_layout = self.createHeader()
        layout.addLayout(header_layout)

        # Onglets d'analytics
        tabs = QTabWidget()

        # Onglet Insights IA
        insights_tab = self.createInsightsTab()
        tabs.addTab(insights_tab, "🤖 Insights IA")

        # Onglet Prédictions
        predictions_tab = self.createPredictionsTab()
        tabs.addTab(predictions_tab, "🔮 Prédictions")

        # Onglet Performance
        performance_tab = self.createPerformanceTab()
        tabs.addTab(performance_tab, "📈 Performance")

        layout.addWidget(tabs)

    def createHeader(self):
        """Crée l'en-tête du dashboard."""
        layout = QHBoxLayout()

        # Titre
        title_label = QLabel("Analytics Intelligent")
        title_label.setFont(QFont("Segoe UI", 24, QFont.Bold))

        subtitle_label = QLabel("Insights automatiques et analyse prédictive")
        subtitle_label.setFont(QFont("Segoe UI", 12))
        subtitle_label.setStyleSheet("color: #6B7280;")

        title_layout = QVBoxLayout()
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        # Boutons d'action
        refresh_btn = PushButton("Analyser")
        refresh_btn.setIcon(FluentIcon.SYNC.icon())
        refresh_btn.clicked.connect(self.start_analysis)

        export_btn = PushButton("Exporter")
        export_btn.setIcon(FluentIcon.DOWNLOAD.icon())
        export_btn.clicked.connect(self.export_insights)

        layout.addLayout(title_layout)
        layout.addStretch()
        layout.addWidget(refresh_btn)
        layout.addWidget(export_btn)

        return layout

    def createInsightsTab(self):
        """Crée l'onglet des insights IA."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Barre de progression d'analyse
        self.analysis_progress = QProgressBar()
        self.analysis_progress.setVisible(False)
        layout.addWidget(self.analysis_progress)

        # Zone de défilement pour les insights
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.insights_widget = QWidget()
        self.insights_layout = QVBoxLayout(self.insights_widget)
        self.insights_layout.setSpacing(15)

        scroll_area.setWidget(self.insights_widget)
        layout.addWidget(scroll_area)

        # Message par défaut
        self.default_message = QLabel("Cliquez sur 'Analyser' pour générer des insights IA")
        self.default_message.setAlignment(Qt.AlignCenter)
        self.default_message.setStyleSheet("color: #6B7280; font-size: 16px; padding: 40px;")
        self.insights_layout.addWidget(self.default_message)

        return widget

    def createPredictionsTab(self):
        """Crée l'onglet des prédictions."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Graphique de prédiction des ventes
        prediction_chart = self.create_prediction_chart()
        layout.addWidget(prediction_chart)

        # Métriques prédictives
        metrics_layout = QHBoxLayout()

        metrics = [
            ("Ventes prévues (30j)", "285 670 DA", "+12%", "#10B981"),
            ("Clients potentiels", "47", "+8%", "#3B82F6"),
            ("Risque de rupture", "3 produits", "-2%", "#EF4444"),
            ("Opportunité revenus", "45 230 DA", "+15%", "#F59E0B")
        ]

        for title, value, trend, color in metrics:
            card = self.create_metric_card(title, value, trend, color)
            metrics_layout.addWidget(card)

        layout.addLayout(metrics_layout)

        return widget

    def createPerformanceTab(self):
        """Crée l'onglet de performance."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # KPIs de performance
        kpi_layout = QGridLayout()

        kpis = [
            ("Précision IA", "94.2%", FluentIcon.ACCEPT),
            ("Temps d'analyse", "2.3s", FluentIcon.SPEED_HIGH),
            ("Insights générés", "127", FluentIcon.EMOJI_TAB_SYMBOLS),
            ("Actions réalisées", "89%", FluentIcon.COMPLETED)
        ]

        for i, (title, value, icon) in enumerate(kpis):
            card = self.create_kpi_card(title, value, icon)
            kpi_layout.addWidget(card, i // 2, i % 2)

        layout.addLayout(kpi_layout)

        # Historique des analyses
        history_label = QLabel("Historique des Analyses")
        history_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        layout.addWidget(history_label)

        self.history_text = QTextEdit()
        self.history_text.setMaximumHeight(200)
        self.history_text.setReadOnly(True)
        layout.addWidget(self.history_text)

        return widget

    def create_prediction_chart(self):
        """Crée un graphique de prédiction."""
        chart = AnimatedChart("Prédictions de Ventes (30 jours)")

        # Générer des données de prédiction
        base_value = 120000
        for i in range(30):
            # Simuler une tendance avec variations
            trend = i * 800
            variation = random.randint(-5000, 8000)
            seasonal = math.sin(i * 0.2) * 3000
            value = max(0, base_value + trend + variation + seasonal)
            chart.add_data_point(value)

        return chart

    def create_metric_card(self, title, value, trend, color):
        """Crée une carte de métrique."""
        card = QFrame()
        card.setFixedSize(200, 100)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)

        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 10))

        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 18, QFont.Bold))

        trend_label = QLabel(trend)
        trend_label.setFont(QFont("Segoe UI", 12, QFont.Bold))

        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(trend_label)

        # Style
        colors = modern_theme_manager.get_colors()
        card.setStyleSheet(f"""
            QFrame {{
                background: {colors['surface']};
                border: 1px solid {color}40;
                border-left: 4px solid {color};
                border-radius: 8px;
            }}
            QLabel {{
                color: {colors['text']};
                background: transparent;
            }}
        """)

        return card

    def create_kpi_card(self, title, value, icon):
        """Crée une carte KPI."""
        card = QFrame()
        card.setFixedSize(200, 80)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)

        # Icône
        icon_widget = IconWidget(icon, card)
        icon_widget.setFixedSize(32, 32)

        # Texte
        text_layout = QVBoxLayout()

        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 10))

        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 16, QFont.Bold))

        text_layout.addWidget(title_label)
        text_layout.addWidget(value_label)

        layout.addWidget(icon_widget)
        layout.addLayout(text_layout)

        # Style
        colors = modern_theme_manager.get_colors()
        card.setStyleSheet(f"""
            QFrame {{
                background: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 8px;
            }}
            QLabel {{
                color: {colors['text']};
                background: transparent;
            }}
        """)

        return card

    def setupTimers(self):
        """Configure les timers."""
        # Timer pour analyse automatique
        self.auto_analysis_timer = QTimer()
        self.auto_analysis_timer.timeout.connect(self.start_analysis)
        self.auto_analysis_timer.start(300000)  # 5 minutes

    def start_analysis(self):
        """Démarre l'analyse IA."""
        if self.analyzer and self.analyzer.isRunning():
            return

        # Afficher la barre de progression
        self.analysis_progress.setVisible(True)
        self.analysis_progress.setRange(0, 0)  # Mode indéterminé

        # Effacer les insights précédents
        self.clear_insights()

        # Démarrer l'analyseur
        data_sources = self.get_data_sources()
        self.analyzer = PredictiveAnalyzer(data_sources)
        self.analyzer.insightGenerated.connect(self.add_insight)
        self.analyzer.analysisComplete.connect(self.on_analysis_complete)
        self.analyzer.start()

        # Ajouter à l'historique
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.history_text.append(f"[{timestamp}] Analyse IA démarrée...")

    def get_data_sources(self):
        """Récupère les sources de données pour l'analyse."""
        # Simuler des sources de données
        return {
            "sales": [],
            "customers": [],
            "inventory": [],
            "products": []
        }

    def clear_insights(self):
        """Efface tous les insights."""
        while self.insights_layout.count() > 0:
            child = self.insights_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        self.insights = []

    def add_insight(self, insight: AIInsight):
        """Ajoute un insight à l'affichage."""
        # Supprimer le message par défaut
        if hasattr(self, 'default_message') and self.default_message.parent():
            self.default_message.setParent(None)

        # Créer la carte d'insight
        insight_card = InsightCard(insight)
        insight_card.actionRequested.connect(self.show_insight_actions)

        # Animation d'apparition
        insight_card.setStyleSheet("QWidget { opacity: 0; }")
        self.insights_layout.addWidget(insight_card)

        # Animation de fade-in
        self.animate_insight_appearance(insight_card)

        self.insights.append(insight)

    def animate_insight_appearance(self, widget):
        """Anime l'apparition d'un insight."""
        # Note: Animation simplifiée pour la démo
        widget.setStyleSheet("")  # Retirer l'opacité

    def on_analysis_complete(self, insights):
        """Appelé quand l'analyse est terminée."""
        self.analysis_progress.setVisible(False)

        timestamp = datetime.now().strftime("%H:%M:%S")
        self.history_text.append(f"[{timestamp}] Analyse terminée - {len(insights)} insights générés")

        InfoBar.success(
            title="Analyse terminée",
            content=f"{len(insights)} insights générés avec succès",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

    def show_insight_actions(self, insight: AIInsight):
        """Affiche les actions recommandées pour un insight."""
        from ui.components.modern_notifications import show_info

        actions_text = "\n".join([f"• {action}" for action in insight.action_items])
        show_info(
            f"Actions recommandées - {insight.title}",
            actions_text
        )

    def export_insights(self):
        """Exporte les insights en JSON."""
        if not self.insights:
            InfoBar.warning(
                title="Aucun insight",
                content="Lancez d'abord une analyse pour générer des insights",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )
            return

        # Simuler l'export
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"insights_{timestamp}.json"

        InfoBar.success(
            title="Export réussi",
            content=f"Insights exportés vers {filename}",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )
