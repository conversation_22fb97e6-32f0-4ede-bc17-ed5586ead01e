"""
Tests pour les fonctionnalités avancées Phase 3 de GSCOM.
Vérifie les nouveaux modules d'analytics, automation, monitoring, etc.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from datetime import datetime


def test_smart_analytics():
    """Test du système d'analytics intelligent."""
    print("🤖 Test du système d'analytics intelligent...")
    
    try:
        from ui.analytics.smart_analytics import SmartAnalyticsDashboard, AIInsight, PredictiveAnalyzer
        
        # Test de création d'insight
        insight = AIInsight(
            "Test Insight",
            "Description de test",
            0.85,
            "Revenue",
            ["Action 1", "Action 2"]
        )
        assert insight.title == "Test Insight"
        assert insight.confidence == 0.85
        assert insight.category == "Revenue"
        assert len(insight.action_items) == 2
        
        # Test de création du dashboard
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            
        dashboard = SmartAnalyticsDashboard()
        assert dashboard is not None
        
        print("✅ Système d'analytics intelligent : OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur analytics intelligent : {e}")
        return False


def test_workflow_system():
    """Test du système de workflows."""
    print("⚙️ Test du système de workflows...")
    
    try:
        from ui.automation.workflow_manager import (
            WorkflowManager, Workflow, WorkflowTrigger, WorkflowAction
        )
        
        # Test de création d'un workflow
        workflow = Workflow("Test Workflow", "Description de test")
        assert workflow.name == "Test Workflow"
        assert workflow.enabled == True
        
        # Test de création d'un déclencheur
        trigger = WorkflowTrigger(
            "condition",
            {"business": {"low_stock": 10}},
            "Stock faible"
        )
        assert trigger.trigger_type == "condition"
        assert trigger.name == "Stock faible"
        
        # Test de création d'une action
        action = WorkflowAction(
            "notification",
            {"message": "Test notification", "type": "info"},
            "Test Action"
        )
        assert action.action_type == "notification"
        assert action.name == "Test Action"
        
        # Test d'ajout au workflow
        workflow.add_trigger(trigger)
        workflow.add_action(action)
        assert len(workflow.triggers) == 1
        assert len(workflow.actions) == 1
        
        print("✅ Système de workflows : OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur système de workflows : {e}")
        return False


def test_plugin_system():
    """Test du système de plugins."""
    print("🔌 Test du système de plugins...")
    
    try:
        from core.plugin_system import (
            plugin_manager, PluginInterface, UIPluginInterface, 
            PluginMetadata, create_sample_plugin
        )
        
        # Test de création d'un plugin d'exemple
        create_sample_plugin()
        
        # Test de découverte des plugins
        plugins = plugin_manager.discover_plugins()
        assert isinstance(plugins, list)
        
        # Test de métadonnées
        if plugins:
            plugin_name = plugins[0]
            info = plugin_manager.get_plugin_info(plugin_name)
            assert "name" in info
            assert "version" in info
            
        print("✅ Système de plugins : OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur système de plugins : {e}")
        return False


def test_monitoring_system():
    """Test du système de monitoring."""
    print("📊 Test du système de monitoring...")
    
    try:
        from ui.monitoring.system_monitor import (
            SystemMonitor, PerformanceMonitor, SystemMetrics
        )
        
        # Test de création des métriques
        metrics = SystemMetrics()
        assert hasattr(metrics, 'cpu_percent')
        assert hasattr(metrics, 'memory_percent')
        assert hasattr(metrics, 'timestamp')
        
        # Test de mise à jour des métriques
        metrics.update()
        assert metrics.cpu_percent >= 0
        assert metrics.memory_percent >= 0
        
        # Test de conversion en dictionnaire
        metrics_dict = metrics.to_dict()
        assert 'cpu_percent' in metrics_dict
        assert 'timestamp' in metrics_dict
        
        print("✅ Système de monitoring : OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur système de monitoring : {e}")
        return False


def test_executive_dashboard():
    """Test du tableau de bord exécutif."""
    print("👔 Test du tableau de bord exécutif...")
    
    try:
        from ui.executive.executive_dashboard import (
            ExecutiveDashboard, KPICard, BusinessMetricsWidget
        )
        
        # Test de création d'une carte KPI
        kpi_card = KPICard(
            "Test KPI",
            1000,
            1200,
            " DA",
            5.5
        )
        assert kpi_card.title == "Test KPI"
        assert kpi_card.current_value == 1000
        assert kpi_card.target_value == 1200
        assert kpi_card.trend == 5.5
        
        # Test de création du widget de métriques
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            
        metrics_widget = BusinessMetricsWidget()
        assert metrics_widget is not None
        
        # Test de création du dashboard exécutif
        exec_dashboard = ExecutiveDashboard()
        assert exec_dashboard is not None
        
        print("✅ Tableau de bord exécutif : OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur tableau de bord exécutif : {e}")
        return False


def test_plugin_manager_ui():
    """Test de l'interface de gestion des plugins."""
    print("🎛️ Test de l'interface de gestion des plugins...")
    
    try:
        from ui.admin.plugin_manager_ui import PluginManagerUI, PluginCard
        
        # Test de création d'une carte de plugin
        plugin_info = {
            "name": "Test Plugin",
            "version": "1.0.0",
            "description": "Plugin de test",
            "author": "Test Author",
            "enabled": True,
            "loaded": False
        }
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            
        plugin_card = PluginCard("test_plugin", plugin_info)
        assert plugin_card.plugin_name == "test_plugin"
        assert plugin_card.plugin_info == plugin_info
        
        # Test de création de l'interface de gestion
        plugin_ui = PluginManagerUI()
        assert plugin_ui is not None
        
        print("✅ Interface de gestion des plugins : OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur interface de gestion des plugins : {e}")
        return False


def test_advanced_config():
    """Test de la configuration avancée Phase 3."""
    print("⚙️ Test de la configuration avancée Phase 3...")
    
    try:
        from config.advanced_config import (
            VERSION, APP_NAME, MODULES_ENABLED, EXPERIMENTAL_FEATURES,
            ensure_directories, is_module_enabled, is_feature_enabled
        )
        
        # Test des constantes
        assert VERSION is not None
        assert APP_NAME is not None
        
        # Test des modules
        assert isinstance(MODULES_ENABLED, dict)
        assert "dashboard" in MODULES_ENABLED
        
        # Test des fonctionnalités expérimentales
        assert isinstance(EXPERIMENTAL_FEATURES, dict)
        
        # Test des fonctions utilitaires
        dashboard_enabled = is_module_enabled("dashboard")
        assert isinstance(dashboard_enabled, bool)
        
        ai_enabled = is_feature_enabled("ai_suggestions")
        assert isinstance(ai_enabled, bool)
        
        # Test de création des dossiers
        ensure_directories()
        
        print("✅ Configuration avancée Phase 3 : OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur configuration avancée : {e}")
        return False


def run_phase3_tests():
    """Exécute tous les tests de la Phase 3."""
    print("🚀 Démarrage des tests Phase 3 - Fonctionnalités Avancées GSCOM\n")
    
    tests = [
        test_smart_analytics,
        test_workflow_system,
        test_plugin_system,
        test_monitoring_system,
        test_executive_dashboard,
        test_plugin_manager_ui,
        test_advanced_config
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Erreur lors du test {test.__name__}: {e}")
            failed += 1
        print()  # Ligne vide entre les tests
        
    print(f"📊 Résultats des tests Phase 3:")
    print(f"   ✅ Tests réussis: {passed}")
    print(f"   ❌ Tests échoués: {failed}")
    print(f"   📈 Taux de réussite: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 Tous les tests Phase 3 sont passés avec succès !")
        print("✅ Les fonctionnalités avancées de GSCOM sont opérationnelles.")
        
        print("\n📋 Nouvelles fonctionnalités testées :")
        print("   🤖 Analytics intelligent avec IA")
        print("   ⚙️ Système de workflows et automatisation")
        print("   🔌 Architecture de plugins extensible")
        print("   📊 Monitoring système en temps réel")
        print("   👔 Tableau de bord exécutif avec KPIs")
        print("   🎛️ Interface de gestion des plugins")
        print("   ⚙️ Configuration avancée modulaire")
        
        print("\n🏆 GSCOM Phase 3 - Niveau Entreprise Avancé atteint !")
        return True
    else:
        print(f"\n💥 {failed} test(s) ont échoué. Vérifiez les erreurs ci-dessus.")
        return False


if __name__ == "__main__":
    # Créer une application Qt pour les tests qui en ont besoin
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    success = run_phase3_tests()
    
    if success:
        print("\n🎯 GSCOM est maintenant une solution complète de niveau entreprise !")
        print("   • Interface moderne et intuitive")
        print("   • Analytics et IA intégrés")
        print("   • Automatisation des processus")
        print("   • Monitoring en temps réel")
        print("   • Architecture extensible")
        print("   • Tableaux de bord exécutifs")
        print("   • Gestion avancée des plugins")
        
        sys.exit(0)
    else:
        print("\n🔧 Certaines fonctionnalités nécessitent des corrections.")
        sys.exit(1)
