# -*- coding: utf-8 -*-
import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QDialog
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap
from ui.splash_screen import SplashScreen

# Imports locaux (relatifs au projet)
import config # Charger la configuration
from database.session import init_db # Fonction pour créer les tables
from views.main_window import MainWindow # La fenêtre principale
from utils.theme_manager import theme_manager # Gestionnaire de thèmes
from auth.user_manager import user_manager # Gestionnaire d'utilisateurs
from auth.login_window import LoginWindow # Fenêtre de connexion


class SGestApp:
    """Classe principale de l'application SGest."""

    def __init__(self):
        """Initialise l'application SGest."""
        print("INFO: Initialisation de SGestApp...")

        # Initialiser la base de données
        try:
            init_db()
        except Exception as e:
            print(f"ERREUR: Impossible d'initialiser la base de données: {e}")

        # Créer la fenêtre principale
        self.main_window = MainWindow()
        print("INFO: SGestApp initialisée.")

    def show(self):
        """Affiche la fenêtre principale."""
        self.main_window.show()

    def close(self):
        """Ferme l'application."""
        if hasattr(self, 'main_window'):
            self.main_window.close()


def main():
    print(f"--- Démarrage de l'application ---")
    print(f"Version Python: {sys.version}")
    print(f"Base de données utilisée: {config.DATABASE_URL}")

    # --- Initialisation de la base de données ---
    # Crée les tables si elles n'existent pas. À exécuter une fois ou vérifier l'existence.
    try:
        init_db()
    except Exception as e_db:
        print(f"ERREUR CRITIQUE lors de l'initialisation de la base de données: {e_db}")
        traceback.print_exc()
        # Décider si l'application peut continuer sans DB fonctionnelle
        # return 1 # Quitter avec un code d'erreur

    # --- Configuration de l'application PyQt ---
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)

    app = QApplication(sys.argv)

    # --- Définir le Thème Fluent ---
    try:
        # Initialiser le thème à partir de la configuration
        from qfluentwidgets import Theme
        if config.DEFAULT_THEME.lower() == 'light':
            theme_manager.set_theme(Theme.LIGHT)
        else:
            theme_manager.set_theme(Theme.DARK)
        print(f"INFO: Thème '{config.DEFAULT_THEME}' appliqué.")
    except Exception as e_theme:
        print(f"AVERTISSEMENT: Impossible d'appliquer le thème '{config.DEFAULT_THEME}': {e_theme}")


    # --- Créer un écran de démarrage personnalisé (optionnel) ---
    splash = None
    try:
        # Créer l'écran de démarrage seulement si l'image existe
        splash_pixmap = None
        if hasattr(config, 'SPLASH_IMAGE') and config.SPLASH_IMAGE and os.path.exists(config.SPLASH_IMAGE):
            splash_pixmap = QPixmap(config.SPLASH_IMAGE)
            splash = SplashScreen(splash_pixmap)

            # Fonction à exécuter lorsque l'écran de démarrage est terminé
            def on_splash_finished():
                # Ne rien faire, le code continuera après le démarrage de l'écran
                pass

            # Connecter le signal de fin
            splash.finished.connect(on_splash_finished)

            # Démarrer l'écran de démarrage
            splash.start(2000)  # 2 secondes seulement
            app.processEvents()
        else:
            print("INFO: Pas d'écran de démarrage (image non trouvée)")
    except Exception as e_splash:
        print(f"AVERTISSEMENT: Impossible d'afficher l'écran de démarrage: {e_splash}")
        splash = None

    # --- Vérifier si une session utilisateur est active ---
    mainWindow = None

    # Pour les besoins de démonstration, forcer l'affichage de l'écran de connexion
    # Vérifier si une session utilisateur est active
    if user_manager.check_session():
        print(f"INFO: Session utilisateur active pour {user_manager.get_current_user().username}")
        try:
            mainWindow = MainWindow()

            # Fermer l'écran de démarrage si présent
            if splash:
                splash.finish(mainWindow)

            mainWindow.show()
            print("INFO: Fenêtre principale affichée.")
        except Exception as e_main_window:
            print(f"ERREUR CRITIQUE lors de la création/affichage de MainWindow: {e_main_window}")
            traceback.print_exc()
            return 1 # Quitter avec un code d'erreur
    else:
        # Fermer l'écran de démarrage si présent
        if splash:
            splash.close()

        # Afficher la fenêtre de connexion moderne
        print("INFO: Création de la fenêtre de connexion moderne...")
        from auth.modern_login_window import ModernLoginWindow
        loginWindow = ModernLoginWindow()

        def on_login_successful():
            # Créer et afficher la fenêtre principale après connexion
            nonlocal mainWindow
            try:
                print("INFO: Connexion réussie, création de la fenêtre principale...")

                # Fermer l'écran de démarrage si présent
                if splash:
                    splash.close()

                # Créer la fenêtre principale
                mainWindow = MainWindow()

                # Importer QTimer ici pour éviter les problèmes de portée
                from PyQt5.QtCore import QTimer, Qt
                from PyQt5.QtWidgets import QApplication

                def show_main_window():
                    print("INFO: Affichage de la fenêtre principale...")

                    # S'assurer que le tableau de bord est visible (nouvelle structure)
                    if hasattr(mainWindow, 'dashboard'):
                        mainWindow.dashboard.setVisible(True)
                        mainWindow.dashboard.setAttribute(Qt.WA_StyledBackground, True)

                    # S'assurer que le tableau de bord est sélectionné comme vue initiale
                    if hasattr(mainWindow, 'dashboard') and hasattr(mainWindow, 'stackedWidget'):
                        try:
                            mainWindow.stackedWidget.setCurrentWidget(mainWindow.dashboard)
                            print("INFO: Tableau de bord défini comme widget courant.")
                        except Exception as e:
                            print(f"ERREUR: Impossible de définir le tableau de bord comme widget courant: {e}")

                    # Afficher la fenêtre principale
                    mainWindow.show()
                    mainWindow.raise_()
                    mainWindow.activateWindow()

                    # Forcer le traitement des événements
                    QApplication.processEvents()

                    print("INFO: Fenêtre principale affichée après connexion.")

                # Délai court pour permettre l'initialisation
                QTimer.singleShot(100, show_main_window)

            except Exception as e_main_window:
                print(f"ERREUR CRITIQUE lors de la création/affichage de MainWindow: {e_main_window}")
                traceback.print_exc()
                return 1 # Quitter avec un code d'erreur

        # Connecter le signal de connexion réussie
        loginWindow.loginSuccessful.connect(on_login_successful)

        # Afficher la fenêtre de connexion avec gestion d'erreur
        try:
            print("INFO: Affichage de la fenêtre de connexion...")
            print("INFO: Utilisez admin/admin pour vous connecter")

            result = loginWindow.exec_()
            if result != QDialog.Accepted:
                print("INFO: Connexion annulée par l'utilisateur.")
                return 0  # Sortir sans erreur
        except Exception as e_login:
            print(f"ERREUR: Problème avec la fenêtre de connexion: {e_login}")
            traceback.print_exc()
            return 1

    # --- Lancer la boucle d'événements ---
    print("INFO: Démarrage de la boucle d'événements Qt...")
    exit_code = app.exec_()
    print(f"--- Application terminée (Code: {exit_code}) ---")
    return exit_code


if __name__ == "__main__":
    # Vérifier si on veut utiliser SGestApp directement
    if len(sys.argv) > 1 and sys.argv[1] == "--direct":
        # Mode direct avec SGestApp
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)

        app = QApplication(sys.argv)

        try:
            sgest_app = SGestApp()
            sgest_app.show()
            sys.exit(app.exec_())
        except Exception as e:
            print(f"ERREUR: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    else:
        # Mode normal avec authentification
        sys.exit(main())