"""
Interface de connexion moderne et futuriste pour GSCOM.
Design glassmorphism avec animations fluides et effets visuels avancés.
"""
import os
import math
import random
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QGraphicsDropShadowEffect, QGraphicsBlurEffect, QWidget,
    QStackedWidget, QProgressBar, QApplication
)
from PyQt5.QtCore import (
    Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve,
    QParallelAnimationGroup, QSequentialAnimationGroup, QRect, QPoint
)
from PyQt5.QtGui import (
    QFont, QPainter, QPen, QBrush, QColor, QLinearGradient,
    QRadialGradient, QPixmap, QPainterPath, QFontDatabase
)
from qfluentwidgets import (
    LineEdit, PrimaryPushButton, TransparentPushButton,
    CheckBox, PasswordLineEdit, InfoBar, InfoBarPosition,
    FluentIcon, Theme, isDarkTheme
)
from .user_manager import user_manager
from utils.theme_manager import theme_manager


class FloatingParticle(QWidget):
    """Particule flottante pour l'arrière-plan."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(4, 4)
        self.setAttribute(Qt.WA_TransparentForMouseEvents)

        # Propriétés de la particule
        self.opacity = random.uniform(0.3, 0.8)
        self.speed = random.uniform(0.5, 2.0)
        self.direction = random.uniform(0, 360)

        # Animation de mouvement
        self.timer = QTimer()
        self.timer.timeout.connect(self.move_particle)
        self.timer.start(50)

    def paintEvent(self, event):
        """Dessine la particule."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Couleur avec opacité
        color = QColor(0, 255, 255, int(self.opacity * 255))
        painter.setBrush(QBrush(color))
        painter.setPen(Qt.NoPen)

        # Dessiner un cercle
        painter.drawEllipse(0, 0, 4, 4)

    def move_particle(self):
        """Déplace la particule."""
        if not self.parent():
            return

        # Calculer la nouvelle position
        dx = math.cos(math.radians(self.direction)) * self.speed
        dy = math.sin(math.radians(self.direction)) * self.speed

        new_x = self.x() + dx
        new_y = self.y() + dy

        # Rebondir sur les bords
        parent_rect = self.parent().rect()
        if new_x <= 0 or new_x >= parent_rect.width() - 4:
            self.direction = 180 - self.direction
        if new_y <= 0 or new_y >= parent_rect.height() - 4:
            self.direction = -self.direction

        # Garder dans les limites
        new_x = max(0, min(new_x, parent_rect.width() - 4))
        new_y = max(0, min(new_y, parent_rect.height() - 4))

        self.move(int(new_x), int(new_y))


class GlassmorphismFrame(QFrame):
    """Frame avec effet glassmorphism."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.NoFrame)

    def paintEvent(self, event):
        """Dessine l'effet glassmorphism."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Créer un chemin arrondi
        path = QPainterPath()
        rect = self.rect()
        from PyQt5.QtCore import QRectF
        rectf = QRectF(rect)
        path.addRoundedRect(rectf, 20, 20)

        # Fond semi-transparent
        if isDarkTheme():
            bg_color = QColor(30, 30, 50, 120)
            border_color = QColor(255, 255, 255, 30)
        else:
            bg_color = QColor(255, 255, 255, 150)
            border_color = QColor(0, 0, 0, 30)

        painter.fillPath(path, QBrush(bg_color))

        # Bordure
        pen = QPen(border_color)
        pen.setWidth(1)
        painter.setPen(pen)
        painter.drawPath(path)


class ModernLoginWindow(QDialog):
    """Interface de connexion moderne avec design glassmorphism."""

    loginSuccessful = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupWindow()
        self.setupUI()
        self.setupAnimations()
        self.setupParticles()

        # Connecter les signaux
        theme_manager.themeChanged.connect(self.updateTheme)

    def setupWindow(self):
        """Configure la fenêtre."""
        self.setWindowTitle("GSCOM - Connexion")
        self.setFixedSize(1000, 650)
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Variables pour le déplacement
        self.drag_position = None

    def setupUI(self):
        """Configure l'interface utilisateur."""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Conteneur principal avec effet glassmorphism
        self.main_container = GlassmorphismFrame()

        # Ombre portée
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setXOffset(0)
        shadow.setYOffset(10)
        shadow.setColor(QColor(0, 0, 0, 100))
        self.main_container.setGraphicsEffect(shadow)

        # Layout du conteneur
        container_layout = QHBoxLayout(self.main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # Panneau gauche (visuel)
        self.left_panel = self.createLeftPanel()
        container_layout.addWidget(self.left_panel, 3)

        # Panneau droit (formulaire)
        self.right_panel = self.createRightPanel()
        container_layout.addWidget(self.right_panel, 2)

        main_layout.addWidget(self.main_container)

    def createLeftPanel(self):
        """Crée le panneau gauche avec les effets visuels."""
        panel = QFrame()
        panel.setObjectName("leftPanel")

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setAlignment(Qt.AlignCenter)

        # Logo/Icône principale
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_pixmap = FluentIcon.HOME.icon().pixmap(120, 120)
        logo_label.setPixmap(logo_pixmap)

        # Titre principal
        title_label = QLabel("GSCOM")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("mainTitle")

        # Sous-titre
        subtitle_label = QLabel("Système de Gestion Commerciale")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitle")
        subtitle_label.setWordWrap(True)

        # Description
        desc_label = QLabel("Interface moderne et intuitive pour la gestion complète de votre activité commerciale")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setObjectName("description")
        desc_label.setWordWrap(True)

        layout.addStretch()
        layout.addWidget(logo_label)
        layout.addSpacing(20)
        layout.addWidget(title_label)
        layout.addSpacing(10)
        layout.addWidget(subtitle_label)
        layout.addSpacing(20)
        layout.addWidget(desc_label)
        layout.addStretch()

        # Style du panneau
        panel.setStyleSheet("""
            QFrame#leftPanel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 150, 255, 0.1),
                    stop:0.5 rgba(100, 200, 255, 0.05),
                    stop:1 rgba(0, 255, 200, 0.1));
                border-top-left-radius: 20px;
                border-bottom-left-radius: 20px;
            }
            QLabel#mainTitle {
                color: #00BFFF;
                font-size: 42px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel#subtitle {
                color: #87CEEB;
                font-size: 16px;
                font-weight: 500;
            }
            QLabel#description {
                color: #B0C4DE;
                font-size: 14px;
                line-height: 1.4;
            }
        """)

        return panel

    def createRightPanel(self):
        """Crée le panneau droit avec le formulaire de connexion."""
        panel = QFrame()
        panel.setObjectName("rightPanel")

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(50, 40, 50, 40)
        layout.setSpacing(25)

        # Bouton de fermeture
        close_layout = QHBoxLayout()
        close_layout.addStretch()

        self.close_button = TransparentPushButton()
        self.close_button.setIcon(FluentIcon.CLOSE.icon())
        self.close_button.setFixedSize(32, 32)
        self.close_button.clicked.connect(self.close)
        close_layout.addWidget(self.close_button)

        # Titre du formulaire
        form_title = QLabel("Connexion")
        form_title.setAlignment(Qt.AlignCenter)
        form_title.setObjectName("formTitle")

        # Sous-titre
        form_subtitle = QLabel("Accédez à votre espace de gestion")
        form_subtitle.setAlignment(Qt.AlignCenter)
        form_subtitle.setObjectName("formSubtitle")

        # Formulaire
        form_layout = QVBoxLayout()
        form_layout.setSpacing(20)

        # Nom d'utilisateur
        self.username_edit = LineEdit()
        self.username_edit.setPlaceholderText("Nom d'utilisateur")
        self.username_edit.setMinimumHeight(50)
        self.username_edit.setClearButtonEnabled(True)

        # Mot de passe
        self.password_edit = PasswordLineEdit()
        self.password_edit.setPlaceholderText("Mot de passe")
        self.password_edit.setMinimumHeight(50)

        # Options
        options_layout = QHBoxLayout()
        self.remember_checkbox = CheckBox("Se souvenir de moi")

        self.forgot_button = TransparentPushButton("Mot de passe oublié ?")
        self.forgot_button.clicked.connect(self.forgotPassword)

        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()
        options_layout.addWidget(self.forgot_button)

        # Bouton de connexion
        self.login_button = PrimaryPushButton("Se connecter")
        self.login_button.setMinimumHeight(50)
        self.login_button.clicked.connect(self.login)

        # Bouton de thème
        theme_layout = QHBoxLayout()
        theme_layout.addStretch()

        self.theme_button = TransparentPushButton()
        self.theme_button.setIcon(FluentIcon.CONSTRACT.icon())
        self.theme_button.setFixedSize(32, 32)
        self.theme_button.clicked.connect(self.toggleTheme)
        theme_layout.addWidget(self.theme_button)

        # Assemblage
        form_layout.addWidget(self.username_edit)
        form_layout.addWidget(self.password_edit)
        form_layout.addLayout(options_layout)
        form_layout.addWidget(self.login_button)

        layout.addLayout(close_layout)
        layout.addStretch()
        layout.addWidget(form_title)
        layout.addWidget(form_subtitle)
        layout.addSpacing(30)
        layout.addLayout(form_layout)
        layout.addStretch()
        layout.addLayout(theme_layout)

        # Style du panneau
        panel.setStyleSheet("""
            QFrame#rightPanel {
                background: rgba(255, 255, 255, 0.05);
                border-top-right-radius: 20px;
                border-bottom-right-radius: 20px;
            }
            QLabel#formTitle {
                color: #FFFFFF;
                font-size: 32px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel#formSubtitle {
                color: #B0C4DE;
                font-size: 14px;
            }
        """)

        # Connecter les signaux
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.login)

        return panel

    def setupAnimations(self):
        """Configure les animations d'entrée."""
        # Animation de fondu pour le conteneur principal
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1000)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Animation de glissement pour le panneau gauche
        self.left_slide_animation = QPropertyAnimation(self.left_panel, b"geometry")
        self.left_slide_animation.setDuration(800)
        self.left_slide_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Animation de glissement pour le panneau droit
        self.right_slide_animation = QPropertyAnimation(self.right_panel, b"geometry")
        self.right_slide_animation.setDuration(800)
        self.right_slide_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Groupe d'animations parallèles
        self.animation_group = QParallelAnimationGroup()
        self.animation_group.addAnimation(self.fade_animation)
        self.animation_group.addAnimation(self.left_slide_animation)
        self.animation_group.addAnimation(self.right_slide_animation)

    def setupParticles(self):
        """Configure les particules flottantes."""
        self.particles = []

        # Créer des particules
        for _ in range(15):
            particle = FloatingParticle(self.left_panel)
            # Position aléatoire
            x = random.randint(0, max(1, self.left_panel.width() - 4))
            y = random.randint(0, max(1, self.left_panel.height() - 4))
            particle.move(x, y)
            particle.show()
            self.particles.append(particle)

    def startAnimations(self):
        """Démarre les animations d'entrée."""
        # Configurer les positions de départ
        left_rect = self.left_panel.geometry()
        right_rect = self.right_panel.geometry()

        # Position de départ pour le panneau gauche (vient de la gauche)
        start_left_rect = QRect(left_rect)
        start_left_rect.moveLeft(-left_rect.width())
        self.left_slide_animation.setStartValue(start_left_rect)
        self.left_slide_animation.setEndValue(left_rect)

        # Position de départ pour le panneau droit (vient de la droite)
        start_right_rect = QRect(right_rect)
        start_right_rect.moveLeft(self.width())
        self.right_slide_animation.setStartValue(start_right_rect)
        self.right_slide_animation.setEndValue(right_rect)

        # Démarrer les animations
        self.animation_group.start()

    def login(self):
        """Tente de connecter l'utilisateur."""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()

        if not username or not password:
            self.showError("Veuillez remplir tous les champs")
            return

        # Désactiver le bouton pendant la vérification
        self.login_button.setEnabled(False)
        self.login_button.setText("Connexion en cours...")

        # Simuler un délai de vérification
        QTimer.singleShot(1000, lambda: self.performLogin(username, password))

    def performLogin(self, username, password):
        """Effectue la connexion."""
        success, message = user_manager.authenticate(username, password)

        if success:
            self.showSuccess("Connexion réussie !")
            QTimer.singleShot(1500, self.acceptLogin)
        else:
            self.showError(message)
            self.login_button.setEnabled(True)
            self.login_button.setText("Se connecter")

    def acceptLogin(self):
        """Accepte la connexion et ferme la fenêtre."""
        self.loginSuccessful.emit()
        self.accept()

    def showSuccess(self, message):
        """Affiche un message de succès."""
        InfoBar.success(
            title="Succès",
            content=message,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def showError(self, message):
        """Affiche un message d'erreur."""
        InfoBar.error(
            title="Erreur",
            content=message,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def forgotPassword(self):
        """Gère la récupération de mot de passe."""
        InfoBar.info(
            title="Récupération de mot de passe",
            content="Contactez votre administrateur système",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def toggleTheme(self):
        """Bascule entre les thèmes."""
        if isDarkTheme():
            theme_manager.set_theme(Theme.LIGHT)
        else:
            theme_manager.set_theme(Theme.DARK)

    def updateTheme(self):
        """Met à jour l'interface selon le thème."""
        if isDarkTheme():
            self.theme_button.setIcon(FluentIcon.BRIGHTNESS.icon())
        else:
            self.theme_button.setIcon(FluentIcon.CONSTRACT.icon())

    def mousePressEvent(self, event):
        """Gère le début du déplacement de la fenêtre."""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """Gère le déplacement de la fenêtre."""
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def showEvent(self, event):
        """Appelé lors de l'affichage de la fenêtre."""
        super().showEvent(event)
        # Démarrer les animations après un court délai
        QTimer.singleShot(100, self.startAnimations)

    def resizeEvent(self, event):
        """Appelé lors du redimensionnement."""
        super().resizeEvent(event)
        # Repositionner les particules si nécessaire
        if hasattr(self, 'particles'):
            for particle in self.particles:
                if particle.x() > self.left_panel.width() - 4:
                    particle.move(self.left_panel.width() - 4, particle.y())
                if particle.y() > self.left_panel.height() - 4:
                    particle.move(particle.x(), self.left_panel.height() - 4)