"""
Mock de psutil pour éviter la dépendance externe.
Simule les fonctionnalités de base de psutil pour le monitoring.
"""
import random
import time
from collections import namedtuple


# Structures de données simulées
MemoryInfo = namedtuple('MemoryInfo', ['rss', 'vms'])
DiskUsage = namedtuple('DiskUsage', ['total', 'used', 'free', 'percent'])
NetworkIO = namedtuple('NetworkIO', ['bytes_sent', 'bytes_recv'])
VirtualMemory = namedtuple('VirtualMemory', ['total', 'available', 'percent', 'used', 'free'])


class NoSuchProcess(Exception):
    """Exception pour processus inexistant."""
    pass


class AccessDenied(Exception):
    """Exception pour accès refusé."""
    pass


class Process:
    """Processus simulé."""
    
    def __init__(self, pid):
        self.pid = pid
        self.info = self._generate_process_info()
        
    def _generate_process_info(self):
        """Génère des informations de processus simulées."""
        names = [
            'python.exe', 'chrome.exe', 'explorer.exe', 'notepad.exe',
            'code.exe', 'firefox.exe', 'winword.exe', 'excel.exe',
            'outlook.exe', 'teams.exe', 'discord.exe', 'spotify.exe'
        ]
        
        statuses = ['running', 'sleeping', 'disk-sleep', 'stopped']
        
        return {
            'pid': self.pid,
            'name': random.choice(names),
            'cpu_percent': random.uniform(0, 25),
            'memory_info': MemoryInfo(
                rss=random.randint(10, 500) * 1024 * 1024,  # 10-500 MB
                vms=random.randint(50, 1000) * 1024 * 1024   # 50MB-1GB
            ),
            'status': random.choice(statuses)
        }


def cpu_percent(interval=None):
    """Simule l'utilisation CPU."""
    if interval:
        time.sleep(interval)
    return random.uniform(5, 85)


def virtual_memory():
    """Simule les informations de mémoire virtuelle."""
    total = 16 * 1024 * 1024 * 1024  # 16 GB
    used = random.randint(4, 12) * 1024 * 1024 * 1024  # 4-12 GB
    available = total - used
    percent = (used / total) * 100
    
    return VirtualMemory(
        total=total,
        available=available,
        percent=percent,
        used=used,
        free=available
    )


def disk_usage(path):
    """Simule l'utilisation du disque."""
    total = 500 * 1024 * 1024 * 1024  # 500 GB
    used = random.randint(100, 400) * 1024 * 1024 * 1024  # 100-400 GB
    free = total - used
    percent = (used / total) * 100
    
    return DiskUsage(
        total=total,
        used=used,
        free=free,
        percent=percent
    )


def net_io_counters():
    """Simule les compteurs réseau."""
    return NetworkIO(
        bytes_sent=random.randint(1000000, 10000000),  # 1-10 MB
        bytes_recv=random.randint(5000000, 50000000)   # 5-50 MB
    )


def pids():
    """Simule la liste des PIDs."""
    return list(range(100, 5000, random.randint(10, 50)))


def process_iter(attrs=None):
    """Simule l'itération sur les processus."""
    process_count = random.randint(50, 150)
    
    for i in range(process_count):
        pid = random.randint(100, 9999)
        try:
            process = Process(pid)
            yield process
        except (NoSuchProcess, AccessDenied):
            continue


# Fonctions utilitaires pour la compatibilité
def boot_time():
    """Simule le temps de démarrage."""
    return time.time() - random.randint(3600, 86400)  # 1h à 24h


def cpu_count(logical=True):
    """Simule le nombre de CPUs."""
    if logical:
        return random.choice([4, 8, 12, 16])
    else:
        return random.choice([2, 4, 6, 8])


def cpu_freq():
    """Simule la fréquence CPU."""
    CpuFreq = namedtuple('CpuFreq', ['current', 'min', 'max'])
    return CpuFreq(
        current=random.uniform(2000, 3500),  # 2-3.5 GHz
        min=800,
        max=4000
    )


def sensors_temperatures():
    """Simule les températures des capteurs."""
    return {
        'cpu': [
            namedtuple('Temperature', ['label', 'current', 'high', 'critical'])(
                'CPU', random.uniform(40, 70), 80, 90
            )
        ]
    }


def sensors_battery():
    """Simule l'état de la batterie."""
    Battery = namedtuple('Battery', ['percent', 'secsleft', 'power_plugged'])
    return Battery(
        percent=random.randint(20, 100),
        secsleft=random.randint(3600, 14400) if random.choice([True, False]) else None,
        power_plugged=random.choice([True, False])
    )


# Variables globales pour la simulation
_last_network_io = None
_last_time = None


def get_network_speed():
    """Calcule la vitesse réseau simulée."""
    global _last_network_io, _last_time
    
    current_io = net_io_counters()
    current_time = time.time()
    
    if _last_network_io and _last_time:
        time_delta = current_time - _last_time
        sent_speed = (current_io.bytes_sent - _last_network_io.bytes_sent) / time_delta
        recv_speed = (current_io.bytes_recv - _last_network_io.bytes_recv) / time_delta
    else:
        sent_speed = recv_speed = 0
        
    _last_network_io = current_io
    _last_time = current_time
    
    return sent_speed, recv_speed


# Simulation de l'état du système
def get_system_load():
    """Simule la charge système."""
    return random.uniform(0.1, 2.0)


def get_uptime():
    """Simule le temps de fonctionnement."""
    return time.time() - boot_time()


# Classes d'exception pour la compatibilité
class Error(Exception):
    """Classe de base pour les erreurs psutil."""
    pass


class TimeoutExpired(Error):
    """Exception pour timeout."""
    pass


# Constantes simulées
ABOVE_NORMAL_PRIORITY_CLASS = 32768
BELOW_NORMAL_PRIORITY_CLASS = 16384
HIGH_PRIORITY_CLASS = 128
IDLE_PRIORITY_CLASS = 64
NORMAL_PRIORITY_CLASS = 32
REALTIME_PRIORITY_CLASS = 256

# Status des processus
STATUS_RUNNING = "running"
STATUS_SLEEPING = "sleeping"
STATUS_DISK_SLEEP = "disk-sleep"
STATUS_STOPPED = "stopped"
STATUS_TRACING_STOP = "tracing-stop"
STATUS_ZOMBIE = "zombie"
STATUS_DEAD = "dead"
STATUS_WAKE_KILL = "wake-kill"
STATUS_WAKING = "waking"
STATUS_IDLE = "idle"
STATUS_LOCKED = "locked"
STATUS_WAITING = "waiting"


if __name__ == "__main__":
    # Test du mock
    print("Test du mock psutil:")
    print(f"CPU: {cpu_percent():.1f}%")
    
    mem = virtual_memory()
    print(f"Mémoire: {mem.percent:.1f}% ({mem.used // (1024**3)} GB / {mem.total // (1024**3)} GB)")
    
    disk = disk_usage('/')
    print(f"Disque: {disk.percent:.1f}% ({disk.used // (1024**3)} GB / {disk.total // (1024**3)} GB)")
    
    net = net_io_counters()
    print(f"Réseau: {net.bytes_sent // (1024**2)} MB envoyés, {net.bytes_recv // (1024**2)} MB reçus")
    
    print(f"Processus: {len(list(pids()))} PIDs")
    
    print("\nProcessus actifs:")
    for i, proc in enumerate(process_iter()):
        if i >= 5:  # Limiter à 5 pour le test
            break
        print(f"  PID {proc.info['pid']}: {proc.info['name']} - CPU: {proc.info['cpu_percent']:.1f}%")
