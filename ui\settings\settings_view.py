"""
Vue des paramètres pour l'application GSCOM.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QComboBox, QLineEdit,
    QCheckBox, QSpinBox, QGroupBox, QTabWidget,
    QTextEdit, QFileDialog, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSlot

from qfluentwidgets import (
    FluentIcon, InfoBar, InfoBarPosition, IconWidget,
    PushButton, ComboBox, SwitchButton, Slider
)

from utils.theme_manager import theme_manager
from ui.styles.clean_style import get_clean_style
import config


class SettingsView(QWidget):
    """Vue des paramètres de l'application."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()
        self.loadCurrentSettings()

    def setupUI(self):
        """Configure l'interface utilisateur."""
        # Connecter le signal de changement de thème
        theme_manager.themeChanged.connect(self.onThemeChanged)

        # Appliquer le style initial
        self.updateStyle()

        layout = QVBoxLayout(self)

        # Bandeau supérieur moderne
        topBar = QHBoxLayout()
        titleIcon = IconWidget(FluentIcon.SETTING, self)
        titleIcon.setFixedSize(32, 32)
        titleLabel = QLabel("Paramètres de l'application", self)
        titleLabel.setStyleSheet("font-size: 22px; font-weight: bold; margin-left: 10px;")
        topBar.addWidget(titleIcon)
        topBar.addWidget(titleLabel)
        topBar.addStretch()
        layout.addLayout(topBar)

        # Ombre sous le bandeau supérieur
        shadowFrame = QFrame(self)
        shadowFrame.setFixedHeight(2)
        shadowFrame.setStyleSheet("background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #38BDF8, stop:1 #0ea5e9); border-radius: 1px;")
        layout.addWidget(shadowFrame)

        # Onglets de paramètres
        self.tabWidget = QTabWidget()
        
        # Onglet Général
        self.generalTab = self.createGeneralTab()
        self.tabWidget.addTab(self.generalTab, "Général")

        # Onglet Apparence
        self.appearanceTab = self.createAppearanceTab()
        self.tabWidget.addTab(self.appearanceTab, "Apparence")

        # Onglet Base de données
        self.databaseTab = self.createDatabaseTab()
        self.tabWidget.addTab(self.databaseTab, "Base de données")

        # Onglet Sécurité
        self.securityTab = self.createSecurityTab()
        self.tabWidget.addTab(self.securityTab, "Sécurité")

        layout.addWidget(self.tabWidget)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        self.resetButton = PushButton("Réinitialiser")
        self.resetButton.setIcon(FluentIcon.CANCEL)
        self.resetButton.clicked.connect(self.resetSettings)

        self.saveButton = PushButton("Enregistrer")
        self.saveButton.setIcon(FluentIcon.SAVE)
        self.saveButton.clicked.connect(self.saveSettings)

        buttons_layout.addWidget(self.resetButton)
        buttons_layout.addWidget(self.saveButton)

        layout.addLayout(buttons_layout)

    def createGeneralTab(self):
        """Crée l'onglet des paramètres généraux."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Informations de l'entreprise
        company_group = QGroupBox("Informations de l'entreprise")
        company_layout = QGridLayout(company_group)

        company_layout.addWidget(QLabel("Nom de l'entreprise:"), 0, 0)
        self.companyNameEdit = QLineEdit()
        self.companyNameEdit.setPlaceholderText("Nom de votre entreprise")
        company_layout.addWidget(self.companyNameEdit, 0, 1)

        company_layout.addWidget(QLabel("Adresse:"), 1, 0)
        self.companyAddressEdit = QTextEdit()
        self.companyAddressEdit.setMaximumHeight(80)
        company_layout.addWidget(self.companyAddressEdit, 1, 1)

        company_layout.addWidget(QLabel("Téléphone:"), 2, 0)
        self.companyPhoneEdit = QLineEdit()
        company_layout.addWidget(self.companyPhoneEdit, 2, 1)

        company_layout.addWidget(QLabel("Email:"), 3, 0)
        self.companyEmailEdit = QLineEdit()
        company_layout.addWidget(self.companyEmailEdit, 3, 1)

        layout.addWidget(company_group)

        # Paramètres de l'application
        app_group = QGroupBox("Paramètres de l'application")
        app_layout = QGridLayout(app_group)

        app_layout.addWidget(QLabel("Langue:"), 0, 0)
        self.languageCombo = ComboBox()
        self.languageCombo.addItems(["Français", "العربية", "English"])
        app_layout.addWidget(self.languageCombo, 0, 1)

        app_layout.addWidget(QLabel("Devise par défaut:"), 1, 0)
        self.currencyCombo = ComboBox()
        self.currencyCombo.addItems(["DA (Dinar Algérien)", "EUR (Euro)", "USD (Dollar)"])
        app_layout.addWidget(self.currencyCombo, 1, 1)

        app_layout.addWidget(QLabel("Démarrage automatique:"), 2, 0)
        self.autostartCheck = QCheckBox("Démarrer avec Windows")
        app_layout.addWidget(self.autostartCheck, 2, 1)

        layout.addWidget(app_group)
        layout.addStretch()

        return tab

    def createAppearanceTab(self):
        """Crée l'onglet des paramètres d'apparence."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Thème
        theme_group = QGroupBox("Thème")
        theme_layout = QGridLayout(theme_group)

        theme_layout.addWidget(QLabel("Thème:"), 0, 0)
        self.themeCombo = ComboBox()
        self.themeCombo.addItems(["Sombre", "Clair", "Système"])
        self.themeCombo.currentTextChanged.connect(self.onThemeComboChanged)
        theme_layout.addWidget(self.themeCombo, 0, 1)

        theme_layout.addWidget(QLabel("Effets visuels:"), 1, 0)
        self.visualEffectsCheck = QCheckBox("Activer les effets visuels")
        theme_layout.addWidget(self.visualEffectsCheck, 1, 1)

        theme_layout.addWidget(QLabel("Animations:"), 2, 0)
        self.animationsCheck = QCheckBox("Activer les animations")
        theme_layout.addWidget(self.animationsCheck, 2, 1)

        layout.addWidget(theme_group)

        # Police
        font_group = QGroupBox("Police")
        font_layout = QGridLayout(font_group)

        font_layout.addWidget(QLabel("Taille de police:"), 0, 0)
        self.fontSizeSpinBox = QSpinBox()
        self.fontSizeSpinBox.setRange(8, 24)
        self.fontSizeSpinBox.setValue(14)
        font_layout.addWidget(self.fontSizeSpinBox, 0, 1)

        font_layout.addWidget(QLabel("Police système:"), 1, 0)
        self.fontFamilyCombo = ComboBox()
        self.fontFamilyCombo.addItems(["Segoe UI", "Arial", "Calibri", "Tahoma"])
        font_layout.addWidget(self.fontFamilyCombo, 1, 1)

        layout.addWidget(font_group)
        layout.addStretch()

        return tab

    def createDatabaseTab(self):
        """Crée l'onglet des paramètres de base de données."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuration de la base de données
        db_group = QGroupBox("Configuration de la base de données")
        db_layout = QGridLayout(db_group)

        db_layout.addWidget(QLabel("Type de base:"), 0, 0)
        self.dbTypeCombo = ComboBox()
        self.dbTypeCombo.addItems(["SQLite (Local)", "MySQL", "PostgreSQL"])
        db_layout.addWidget(self.dbTypeCombo, 0, 1)

        db_layout.addWidget(QLabel("Fichier de base:"), 1, 0)
        db_file_layout = QHBoxLayout()
        self.dbFileEdit = QLineEdit()
        self.dbFileEdit.setReadOnly(True)
        self.browseDbButton = QPushButton("Parcourir...")
        self.browseDbButton.clicked.connect(self.browseDatabase)
        db_file_layout.addWidget(self.dbFileEdit)
        db_file_layout.addWidget(self.browseDbButton)
        db_layout.addLayout(db_file_layout, 1, 1)

        layout.addWidget(db_group)

        # Sauvegarde
        backup_group = QGroupBox("Sauvegarde automatique")
        backup_layout = QGridLayout(backup_group)

        backup_layout.addWidget(QLabel("Sauvegarde auto:"), 0, 0)
        self.autoBackupCheck = QCheckBox("Activer la sauvegarde automatique")
        backup_layout.addWidget(self.autoBackupCheck, 0, 1)

        backup_layout.addWidget(QLabel("Fréquence:"), 1, 0)
        self.backupFrequencyCombo = ComboBox()
        self.backupFrequencyCombo.addItems(["Quotidienne", "Hebdomadaire", "Mensuelle"])
        backup_layout.addWidget(self.backupFrequencyCombo, 1, 1)

        backup_layout.addWidget(QLabel("Dossier de sauvegarde:"), 2, 0)
        backup_folder_layout = QHBoxLayout()
        self.backupFolderEdit = QLineEdit()
        self.browseBackupButton = QPushButton("Parcourir...")
        self.browseBackupButton.clicked.connect(self.browseBackupFolder)
        backup_folder_layout.addWidget(self.backupFolderEdit)
        backup_folder_layout.addWidget(self.browseBackupButton)
        backup_layout.addLayout(backup_folder_layout, 2, 1)

        layout.addWidget(backup_group)
        layout.addStretch()

        return tab

    def createSecurityTab(self):
        """Crée l'onglet des paramètres de sécurité."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Sécurité
        security_group = QGroupBox("Paramètres de sécurité")
        security_layout = QGridLayout(security_group)

        security_layout.addWidget(QLabel("Verrouillage auto:"), 0, 0)
        self.autoLockCheck = QCheckBox("Verrouiller après inactivité")
        security_layout.addWidget(self.autoLockCheck, 0, 1)

        security_layout.addWidget(QLabel("Délai (minutes):"), 1, 0)
        self.lockTimeoutSpinBox = QSpinBox()
        self.lockTimeoutSpinBox.setRange(1, 120)
        self.lockTimeoutSpinBox.setValue(30)
        security_layout.addWidget(self.lockTimeoutSpinBox, 1, 1)

        security_layout.addWidget(QLabel("Journalisation:"), 2, 0)
        self.loggingCheck = QCheckBox("Enregistrer les actions utilisateur")
        security_layout.addWidget(self.loggingCheck, 2, 1)

        layout.addWidget(security_group)
        layout.addStretch()

        return tab

    def loadCurrentSettings(self):
        """Charge les paramètres actuels."""
        # Charger les paramètres depuis config
        self.companyNameEdit.setText(getattr(config, 'COMPANY_NAME', ''))
        self.companyAddressEdit.setPlainText(getattr(config, 'COMPANY_ADDRESS', ''))
        self.companyPhoneEdit.setText(getattr(config, 'COMPANY_PHONE', ''))
        self.companyEmailEdit.setText(getattr(config, 'COMPANY_EMAIL', ''))
        
        # Thème actuel
        current_theme = getattr(config, 'DEFAULT_THEME', 'Dark')
        if current_theme.lower() == 'dark':
            self.themeCombo.setCurrentText("Sombre")
        elif current_theme.lower() == 'light':
            self.themeCombo.setCurrentText("Clair")
        else:
            self.themeCombo.setCurrentText("Système")

    def onThemeComboChanged(self, theme_text):
        """Appelé quand le thème change dans la combo."""
        if theme_text == "Sombre":
            theme_manager.set_theme("Dark")
        elif theme_text == "Clair":
            theme_manager.set_theme("Light")
        else:
            theme_manager.set_theme("Auto")

    def browseDatabase(self):
        """Parcourir pour sélectionner un fichier de base de données."""
        filename, _ = QFileDialog.getOpenFileName(
            self,
            "Sélectionner le fichier de base de données",
            "",
            "Fichiers SQLite (*.db *.sqlite);;Tous les fichiers (*)"
        )
        if filename:
            self.dbFileEdit.setText(filename)

    def browseBackupFolder(self):
        """Parcourir pour sélectionner le dossier de sauvegarde."""
        folder = QFileDialog.getExistingDirectory(
            self,
            "Sélectionner le dossier de sauvegarde"
        )
        if folder:
            self.backupFolderEdit.setText(folder)

    def saveSettings(self):
        """Sauvegarde les paramètres."""
        # Ici vous devriez sauvegarder les paramètres dans config ou base de données
        InfoBar.success(
            title="Paramètres sauvegardés",
            content="Les paramètres ont été sauvegardés avec succès.",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

    def resetSettings(self):
        """Réinitialise les paramètres par défaut."""
        reply = QMessageBox.question(
            self,
            "Réinitialiser les paramètres",
            "Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.loadCurrentSettings()
            InfoBar.success(
                title="Paramètres réinitialisés",
                content="Les paramètres ont été réinitialisés aux valeurs par défaut.",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )

    def updateStyle(self):
        """Met à jour le style en fonction du thème actuel"""
        self.setStyleSheet(get_clean_style())

    @pyqtSlot(object)
    def onThemeChanged(self, _):
        """Appelé lorsque le thème change"""
        self.updateStyle()
