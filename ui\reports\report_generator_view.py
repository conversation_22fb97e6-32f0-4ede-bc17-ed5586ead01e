"""
Vue de génération de rapports pour l'application GSCOM.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QComboBox, QDateEdit,
    QTextEdit, QProgressBar, QGroupBox, QCheckBox
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSlot
from PyQt5.QtGui import QFont

from qfluentwidgets import (
    FluentIcon, InfoBar, InfoBarPosition, IconWidget,
    CardWidget, PushButton, ComboBox
)

from utils.theme_manager import theme_manager
from ui.styles.clean_style import get_clean_style


class ReportGeneratorView(QWidget):
    """Vue de génération de rapports."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUI()

    def setupUI(self):
        """Configure l'interface utilisateur."""
        # Connecter le signal de changement de thème
        theme_manager.themeChanged.connect(self.onThemeChanged)

        # Appliquer le style initial
        self.updateStyle()

        layout = QVBoxLayout(self)

        # Bandeau supérieur moderne
        topBar = QHBoxLayout()
        titleIcon = IconWidget(FluentIcon.DOCUMENT, self)
        titleIcon.setFixedSize(32, 32)
        titleLabel = QLabel("Générateur de rapports", self)
        titleLabel.setStyleSheet("font-size: 22px; font-weight: bold; margin-left: 10px;")
        topBar.addWidget(titleIcon)
        topBar.addWidget(titleLabel)
        topBar.addStretch()
        layout.addLayout(topBar)

        # Ombre sous le bandeau supérieur
        shadowFrame = QFrame(self)
        shadowFrame.setFixedHeight(2)
        shadowFrame.setStyleSheet("background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #38BDF8, stop:1 #0ea5e9); border-radius: 1px;")
        layout.addWidget(shadowFrame)

        # Contenu principal
        content_layout = QHBoxLayout()

        # Panneau de gauche - Configuration
        left_panel = self.createConfigurationPanel()
        content_layout.addWidget(left_panel, 1)

        # Panneau de droite - Aperçu et actions
        right_panel = self.createPreviewPanel()
        content_layout.addWidget(right_panel, 2)

        layout.addLayout(content_layout)

    def createConfigurationPanel(self):
        """Crée le panneau de configuration des rapports."""
        panel = QGroupBox("Configuration du rapport")
        layout = QVBoxLayout(panel)

        # Type de rapport
        type_layout = QVBoxLayout()
        type_label = QLabel("Type de rapport:")
        type_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        
        self.reportTypeCombo = ComboBox()
        self.reportTypeCombo.addItems([
            "Rapport de ventes",
            "Rapport d'inventaire", 
            "Rapport financier",
            "Rapport clients",
            "Rapport fournisseurs",
            "Rapport de mouvements de stock"
        ])
        self.reportTypeCombo.currentTextChanged.connect(self.onReportTypeChanged)

        type_layout.addWidget(type_label)
        type_layout.addWidget(self.reportTypeCombo)
        layout.addLayout(type_layout)

        # Période
        period_layout = QVBoxLayout()
        period_label = QLabel("Période:")
        period_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")

        date_layout = QHBoxLayout()
        self.startDateEdit = QDateEdit()
        self.startDateEdit.setDate(QDate.currentDate().addDays(-30))
        self.startDateEdit.setCalendarPopup(True)

        self.endDateEdit = QDateEdit()
        self.endDateEdit.setDate(QDate.currentDate())
        self.endDateEdit.setCalendarPopup(True)

        date_layout.addWidget(QLabel("Du:"))
        date_layout.addWidget(self.startDateEdit)
        date_layout.addWidget(QLabel("Au:"))
        date_layout.addWidget(self.endDateEdit)

        period_layout.addWidget(period_label)
        period_layout.addLayout(date_layout)
        layout.addLayout(period_layout)

        # Options
        options_layout = QVBoxLayout()
        options_label = QLabel("Options:")
        options_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")

        self.includeChartsCheck = QCheckBox("Inclure les graphiques")
        self.includeChartsCheck.setChecked(True)

        self.includeDetailsCheck = QCheckBox("Inclure les détails")
        self.includeDetailsCheck.setChecked(True)

        self.includeSummaryCheck = QCheckBox("Inclure le résumé")
        self.includeSummaryCheck.setChecked(True)

        options_layout.addWidget(options_label)
        options_layout.addWidget(self.includeChartsCheck)
        options_layout.addWidget(self.includeDetailsCheck)
        options_layout.addWidget(self.includeSummaryCheck)
        layout.addLayout(options_layout)

        # Format de sortie
        format_layout = QVBoxLayout()
        format_label = QLabel("Format de sortie:")
        format_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")

        self.formatCombo = ComboBox()
        self.formatCombo.addItems(["PDF", "Excel", "CSV", "HTML"])

        format_layout.addWidget(format_label)
        format_layout.addWidget(self.formatCombo)
        layout.addLayout(format_layout)

        layout.addStretch()

        # Boutons d'action
        buttons_layout = QVBoxLayout()

        self.previewButton = PushButton("Aperçu")
        self.previewButton.setIcon(FluentIcon.VIEW)
        self.previewButton.clicked.connect(self.generatePreview)

        self.generateButton = PushButton("Générer le rapport")
        self.generateButton.setIcon(FluentIcon.DOWNLOAD)
        self.generateButton.clicked.connect(self.generateReport)

        buttons_layout.addWidget(self.previewButton)
        buttons_layout.addWidget(self.generateButton)
        layout.addLayout(buttons_layout)

        return panel

    def createPreviewPanel(self):
        """Crée le panneau d'aperçu."""
        panel = QGroupBox("Aperçu du rapport")
        layout = QVBoxLayout(panel)

        # Zone d'aperçu
        self.previewArea = QTextEdit()
        self.previewArea.setReadOnly(True)
        self.previewArea.setPlainText("Sélectionnez un type de rapport et cliquez sur 'Aperçu' pour voir le contenu.")
        layout.addWidget(self.previewArea)

        # Barre de progression
        self.progressBar = QProgressBar()
        self.progressBar.setVisible(False)
        layout.addWidget(self.progressBar)

        # Statistiques rapides
        stats_layout = QGridLayout()
        
        self.totalSalesLabel = QLabel("Ventes totales: 0 DA")
        self.totalItemsLabel = QLabel("Articles vendus: 0")
        self.avgSaleLabel = QLabel("Vente moyenne: 0 DA")
        self.topProductLabel = QLabel("Produit top: -")

        stats_layout.addWidget(self.totalSalesLabel, 0, 0)
        stats_layout.addWidget(self.totalItemsLabel, 0, 1)
        stats_layout.addWidget(self.avgSaleLabel, 1, 0)
        stats_layout.addWidget(self.topProductLabel, 1, 1)

        layout.addLayout(stats_layout)

        return panel

    def onReportTypeChanged(self, report_type):
        """Appelé quand le type de rapport change."""
        # Mettre à jour l'aperçu selon le type
        preview_text = f"Aperçu du {report_type.lower()}\n\n"
        
        if "ventes" in report_type.lower():
            preview_text += "• Résumé des ventes par période\n"
            preview_text += "• Détail des transactions\n"
            preview_text += "• Analyse des tendances\n"
            preview_text += "• Top des produits vendus\n"
        elif "inventaire" in report_type.lower():
            preview_text += "• État actuel du stock\n"
            preview_text += "• Mouvements de stock\n"
            preview_text += "• Produits en rupture\n"
            preview_text += "• Valorisation du stock\n"
        elif "financier" in report_type.lower():
            preview_text += "• Chiffre d'affaires\n"
            preview_text += "• Bénéfices et pertes\n"
            preview_text += "• Créances clients\n"
            preview_text += "• Dettes fournisseurs\n"
        
        self.previewArea.setPlainText(preview_text)

    def generatePreview(self):
        """Génère un aperçu du rapport."""
        self.progressBar.setVisible(True)
        self.progressBar.setValue(0)

        # Simuler la génération
        self.timer = QTimer()
        self.timer.timeout.connect(self.updateProgress)
        self.timer.start(100)

    def updateProgress(self):
        """Met à jour la barre de progression."""
        current = self.progressBar.value()
        if current < 100:
            self.progressBar.setValue(current + 10)
        else:
            self.timer.stop()
            self.progressBar.setVisible(False)
            self.showPreviewResult()

    def showPreviewResult(self):
        """Affiche le résultat de l'aperçu."""
        report_type = self.reportTypeCombo.currentText()
        start_date = self.startDateEdit.date().toString("dd/MM/yyyy")
        end_date = self.endDateEdit.date().toString("dd/MM/yyyy")

        preview_content = f"""
{report_type}
Période: du {start_date} au {end_date}

=== RÉSUMÉ EXÉCUTIF ===
• Nombre total de transactions: 156
• Montant total: 245 670,50 DA
• Croissance par rapport à la période précédente: +12,5%

=== DÉTAILS PAR CATÉGORIE ===
• Électronique: 89 450,00 DA (36,4%)
• Informatique: 78 320,50 DA (31,8%)
• Accessoires: 45 230,00 DA (18,4%)
• Autres: 32 670,00 DA (13,3%)

=== TOP 5 PRODUITS ===
1. Ordinateur portable HP - 25 unités
2. Smartphone Samsung - 18 unités
3. Imprimante Canon - 15 unités
4. Clavier mécanique - 12 unités
5. Souris gaming - 10 unités

=== RECOMMANDATIONS ===
• Augmenter le stock des produits top
• Promouvoir les catégories moins performantes
• Analyser les tendances saisonnières
        """

        self.previewArea.setPlainText(preview_content)

        # Mettre à jour les statistiques
        self.totalSalesLabel.setText("Ventes totales: 245 670,50 DA")
        self.totalItemsLabel.setText("Articles vendus: 156")
        self.avgSaleLabel.setText("Vente moyenne: 1 574,81 DA")
        self.topProductLabel.setText("Produit top: Ordinateur portable HP")

        InfoBar.success(
            title="Aperçu généré",
            content="L'aperçu du rapport a été généré avec succès.",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self
        )

    def generateReport(self):
        """Génère le rapport final."""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        
        report_type = self.reportTypeCombo.currentText()
        format_type = self.formatCombo.currentText()
        
        # Demander où sauvegarder
        filename, _ = QFileDialog.getSaveFileName(
            self,
            f"Sauvegarder {report_type}",
            f"rapport_{report_type.lower().replace(' ', '_')}",
            f"Fichiers {format_type} (*.{format_type.lower()})"
        )

        if filename:
            # Simuler la génération du rapport
            QMessageBox.information(
                self,
                "Rapport généré",
                f"Le {report_type.lower()} a été généré avec succès.\n\nFichier: {filename}"
            )

            InfoBar.success(
                title="Rapport généré",
                content=f"Le rapport a été sauvegardé: {filename}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=5000,
                parent=self
            )

    def updateStyle(self):
        """Met à jour le style en fonction du thème actuel"""
        self.setStyleSheet(get_clean_style())

    @pyqtSlot(object)
    def onThemeChanged(self, _):
        """Appelé lorsque le thème change"""
        self.updateStyle()
