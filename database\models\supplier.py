from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean
from sqlalchemy.orm import relationship
from database.base import Base
from datetime import datetime

class Supplier(Base):
    __tablename__ = 'suppliers'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True)
    name = Column(String(100), nullable=False)
    address = Column(String(200))
    phone = Column(String(20))
    email = Column(String(100))
    contact_person = Column(String(100))
    tax_id = Column(String(50))
    is_active = Column(Boolean, default=True)
    credit_limit = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relations
    products = relationship("Product", back_populates="supplier")
    
    @property
    def total_purchases(self) -> float:
        """Calcule le total des achats pour ce fournisseur"""
        # À implémenter quand les achats seront créés
        return 0.0
