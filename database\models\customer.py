from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float
from sqlalchemy.orm import relationship
from database.base import Base
from datetime import datetime

class Customer(Base):
    __tablename__ = 'customers'

    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True)
    name = Column(String(100), nullable=False)
    address = Column(String(200))
    phone = Column(String(20))
    email = Column(String(100))
    tax_id = Column(String(50))
    is_active = Column(Boolean, default=True)
    credit_limit = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relations
    sales = relationship("Sale", back_populates="customer")

    @property
    def total_sales(self) -> float:
        """Calcule le total des ventes pour ce client"""
        return sum(sale.total_amount for sale in self.sales)