from PyQt5.QtCore import Qt, pyqtSlot, QTimer
from PyQt5.QtWidgets import QApplication, QWidget

from qfluentwidgets import (
    FluentWindow, FluentIcon, NavigationItemPosition,
    SwitchButton, InfoBar, InfoBarPosition
)

# Import theme manager, user manager and permission manager
from utils.theme_manager import theme_manager
from utils.animations import fade_in, fade_out, slide_in
from auth.user_manager import user_manager
from auth.permissions import Permission, permission_manager
from ui.styles.clean_style import get_clean_style

# Importer les vues principales
from .simple_dashboard_view import SimpleDashboardView
from .dashboard.modern_dashboard import ModernDashboard
# Importer les autres vues
from .stock.product_list_view import ProductListView
from .customers.customer_list_view import CustomerListView
from .suppliers.supplier_list_view import SupplierListView
# Réactiver les imports
from .sales.invoice_list_view import InvoiceListView
from .sales.quote_list_view import QuoteListView
from .inventory.stock_movement_view import StockMovementView
from .admin.user_management_view import UserManagementView
from .admin.activity_log_view import ActivityLogView
from .profile_view import ProfileView
# Ajouter la vue de liste des ventes
from .views.sales.sales_list_view import SalesListView
from controllers.sales_controller import SalesController
# Ajouter les rapports
from .reports.report_generator_view import ReportGeneratorView
# Ajouter les paramètres
from .settings.settings_view import SettingsView
# ... et ainsi de suite

class MainWindow(FluentWindow):

    def __init__(self):
        super().__init__()

        print("INFO: Initialisation de MainWindow...")

    def addSubInterface(self, widget, icon, text, position=NavigationItemPosition.TOP):
        """Ajoute une sous-interface à la fenêtre principale."""
        self.stackedWidget.addWidget(widget)
        self.navigationInterface.addItem(
            routeKey=widget.objectName(),
            icon=icon,
            text=text,
            onClick=lambda: self.switchView(widget),
            position=position
        )

    def switchView(self, new_widget):
        """Change la vue courante avec une animation."""
        # Récupérer le widget courant
        current_widget = self.stackedWidget.currentWidget()

        # Si c'est le même widget, ne rien faire
        if current_widget == new_widget:
            return

        # Préparer la nouvelle vue (opacité à 0)
        new_widget.setWindowOpacity(0)

        # Changer la vue
        self.stackedWidget.setCurrentWidget(new_widget)

        # Animer la transition
        fade_in(new_widget, duration=300)

        # Appliquer le style de base
        self.applyBaseStyle()

        # Définir une propriété pour suivre l'état d'initialisation
        self._dashboard_initialized = False
        # Désactive la sélection de texte sur tous les QLabel de la fenêtre principale
        def disable_label_selection(widget):
            from PyQt5.QtWidgets import QLabel
            from PyQt5.QtCore import Qt
            for child in widget.findChildren(QLabel):
                child.setTextInteractionFlags(Qt.NoTextInteraction)
        disable_label_selection(self)

        # --- Création des Widgets d'Interface Principaux ---
        # Chaque widget ajouté doit avoir un objectName unique !

        # Dashboard moderne (principal)
        self.dashboardView = ModernDashboard(self)
        self.dashboardView.setObjectName("dashboardViewUniqueId")

        # Dashboard simple (pour compatibilité)
        self.simpleDashboardView = SimpleDashboardView(self)
        self.simpleDashboardView.setObjectName("simpleDashboardViewUniqueId")

        # Gestion des produits
        self.productListView = ProductListView(self)
        self.productListView.setObjectName("productListViewUniqueId")

        # Gestion des clients
        self.customerListView = CustomerListView(self)
        self.customerListView.setObjectName("customerListViewUniqueId")

        # Gestion des fournisseurs
        self.supplierListView = SupplierListView(self)
        self.supplierListView.setObjectName("supplierListViewUniqueId")

        # Gestion des ventes
        self.invoiceListView = InvoiceListView(self)
        self.invoiceListView.setObjectName("invoiceListViewUniqueId")

        # Module de devis
        self.quoteListView = QuoteListView(self)
        self.quoteListView.setObjectName("quoteListViewUniqueId")

        # Module de liste des ventes
        from database.session import get_session
        self.salesController = SalesController(get_session())
        self.salesListView = SalesListView(self.salesController, self)
        self.salesListView.setObjectName("salesListViewUniqueId")

        # Gestion des stocks
        self.stockMovementView = StockMovementView(self)
        self.stockMovementView.setObjectName("stockMovementViewUniqueId")

        # Gestion des utilisateurs (administration)
        self.userManagementView = UserManagementView(self)
        self.userManagementView.setObjectName("userManagementViewUniqueId")

        # Journaux d'activité (administration)
        self.activityLogView = ActivityLogView(self)
        self.activityLogView.setObjectName("activityLogViewUniqueId")

        # Profil utilisateur
        self.profileView = ProfileView(self)
        self.profileView.setObjectName("profileViewUniqueId")

        # Générateur de rapports
        self.reportGeneratorView = ReportGeneratorView(self)
        self.reportGeneratorView.setObjectName("reportGeneratorViewUniqueId")

        # Vue des paramètres
        self.settingsView = SettingsView(self)
        self.settingsView.setObjectName("settingsViewUniqueId")

        # Initialisation de la navigation
        self.initNavigation()

        # Ajouter le bouton de changement de thème et de déconnexion
        self.setupThemeToggle()
        self.setupLogoutButton()

        # Configuration de la fenêtre
        self.setWindowTitle("Gestion Commerciale Pro")
        self.resize(1200, 800) # Taille initiale

        # Connecter le signal de changement de thème
        theme_manager.themeChanged.connect(self.onThemeChanged)

        # S'assurer que le tableau de bord est visible
        if hasattr(self, 'dashboardView') and hasattr(self, 'stackedWidget'):
            print("INFO: Définition du tableau de bord comme vue par défaut...")
            try:
                self.stackedWidget.setCurrentWidget(self.dashboardView)
                print("INFO: Tableau de bord défini comme vue par défaut.")
            except Exception as e:
                print(f"ERREUR: Impossible de définir le tableau de bord comme vue par défaut: {e}")

        print("INFO: MainWindow initialisée.")

    def setupThemeToggle(self):
        """Configure le bouton de changement de thème"""
        # Ajouter un séparateur en bas
        self.navigationInterface.addSeparator(position=NavigationItemPosition.BOTTOM)

        # Ajouter le bouton de changement de thème
        self.themeSwitch = SwitchButton()
        self.themeSwitch.setChecked(theme_manager.is_dark_theme())
        self.themeSwitch.checkedChanged.connect(self.onThemeSwitchChanged)

        # Ajouter le bouton à la barre de navigation
        self.navigationInterface.addItem(
            routeKey='themeToggle',
            icon=FluentIcon.CONSTRACT,
            text='Thème sombre',
            onClick=lambda: self.themeSwitch.setChecked(not self.themeSwitch.isChecked()),
            position=NavigationItemPosition.BOTTOM
        )

    @pyqtSlot(bool)
    def onThemeSwitchChanged(self, checked):
        """Appelé lorsque le bouton de changement de thème est activé/désactivé"""
        from qfluentwidgets import Theme
        import traceback

        try:
            print(f"DEBUG: Changement de thème via switch: {'DARK' if checked else 'LIGHT'}")

            # Appliquer le thème sélectionné
            if checked:
                # Thème sombre
                theme_manager.set_theme(Theme.DARK)
                theme_name = "sombre"
            else:
                # Thème clair
                theme_manager.set_theme(Theme.LIGHT)
                theme_name = "clair"

            # Afficher une notification
            InfoBar.success(
                title="Thème modifié",
                content=f"Le thème {theme_name} a été appliqué",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )
        except Exception as e:
            print(f"ERREUR: Problème lors du changement de thème: {e}")
            traceback.print_exc()

            # Afficher une notification d'erreur
            InfoBar.error(
                title="Erreur",
                content="Une erreur est survenue lors du changement de thème",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=5000,
                parent=self
            )

    @pyqtSlot(object)
    def onThemeChanged(self, _):
        """Appelé lorsque le thème change"""
        # Mettre à jour le style de la fenêtre principale avec notre style propre
        self.setStyleSheet(get_clean_style())

        # Mettre à jour l'état du bouton de changement de thème
        self.themeSwitch.setChecked(theme_manager.is_dark_theme())

        # Mettre à jour les styles des widgets enfants
        for widget in self.findChildren(QWidget):
            if hasattr(widget, 'onThemeChanged') and widget != self:
                widget.onThemeChanged(_)

    def setupLogoutButton(self):
        """Configure le bouton de déconnexion"""
        # Ajouter un séparateur en bas
        self.navigationInterface.addSeparator(position=NavigationItemPosition.BOTTOM)

        # Ajouter le bouton de profil
        self.navigationInterface.addItem(
            routeKey='profile',
            icon=FluentIcon.PEOPLE,
            text='Profil',
            onClick=lambda: self.stackedWidget.setCurrentWidget(self.profileView),
            position=NavigationItemPosition.BOTTOM
        )

        # Ajouter le bouton de déconnexion
        self.navigationInterface.addItem(
            routeKey='logout',
            icon=FluentIcon.RETURN,
            text='Déconnexion',
            onClick=self.onLogout,
            position=NavigationItemPosition.BOTTOM
        )

    def onLogout(self):
        """Appelé lorsque le bouton de déconnexion est cliqué"""
        # Demander confirmation
        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir vous déconnecter ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Déconnecter l'utilisateur
            user_manager.logout()

            # Afficher une notification
            InfoBar.success(
                title="Déconnexion",
                content="Vous avez été déconnecté avec succès",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self
            )

            # Fermer l'application
            self.close()

            # Redémarrer l'application pour afficher l'écran de connexion
            QApplication.exit(0)


    def initNavigation(self):
        print("INFO: Initialisation de la navigation...")

        # Tableau de Bord
        if permission_manager.has_permission(Permission.VIEW_DASHBOARD):
            print("INFO: Ajout de l'interface 'Tableau de Bord'...")

            # Ajouter le tableau de bord à l'interface
            self.addSubInterface(
                self.dashboardView,
                FluentIcon.HOME,
                "Tableau de Bord"
            )

            # Définir le tableau de bord comme vue par défaut
            try:
                print("INFO: Définition du tableau de bord comme vue par défaut...")
                self.stackedWidget.setCurrentWidget(self.dashboardView)

                # Marquer le tableau de bord comme initialisé
                self._dashboard_initialized = True

                print("INFO: Interface 'Tableau de Bord' ajoutée et définie comme vue par défaut.")
            except Exception as e:
                print(f"ERREUR: Impossible de définir le tableau de bord comme vue par défaut: {e}")

        # Gestion des Stocks
        if permission_manager.has_permission(Permission.VIEW_PRODUCTS):
            self.navigationInterface.addSeparator()
            self.addSubInterface(self.productListView, FluentIcon.SHOPPING_CART, "Catalogue Produits")
            print("INFO: Interface 'Catalogue Produits' ajoutée.")
            # Ajouter d'autres vues du stock comme sous-éléments si souhaité
            # self.navigationInterface.addItem(
            #     routeKey='categoriesView',
            #     icon=FluentIcon.TAG,
            #     text='Catégories',
            #     onClick=lambda: self.stackedWidget.setCurrentWidget(self.categoryView), # Changer pour la bonne vue
            #     position=NavigationItemPosition.TOP,
            #     parent=stock_group # Lier au groupe 'Catalogue Produits'
            # )

        # Gestion des Clients
        if permission_manager.has_permission(Permission.VIEW_CUSTOMERS):
            self.navigationInterface.addSeparator()
            self.addSubInterface(self.customerListView, FluentIcon.PEOPLE, "Clients")
            print("INFO: Interface 'Clients' ajoutée.")

        # Gestion des Fournisseurs
        if permission_manager.has_permission(Permission.VIEW_CUSTOMERS):  # Utiliser la même permission pour l'instant
            self.addSubInterface(self.supplierListView, FluentIcon.MARKET, "Fournisseurs")
            print("INFO: Interface 'Fournisseurs' ajoutée.")

        # Gestion des Ventes - Factures
        if permission_manager.has_permission(Permission.VIEW_INVOICES):
            self.navigationInterface.addSeparator()
            self.addSubInterface(self.invoiceListView, FluentIcon.DOCUMENT, "Factures")
            print("INFO: Interface 'Factures' ajoutée.")

        # Gestion des Ventes - Liste des ventes
        if permission_manager.has_permission(Permission.VIEW_INVOICES):
            self.addSubInterface(self.salesListView, FluentIcon.MARKET, "Ventes")
            print("INFO: Interface 'Ventes' ajoutée.")

        # Gestion des Ventes - Devis
        if permission_manager.has_permission(Permission.VIEW_QUOTES):
            # Ajouter les devis comme sous-élément du groupe Ventes
            self.addSubInterface(
                self.quoteListView,
                FluentIcon.DOCUMENT,
                "Devis",
                NavigationItemPosition.TOP
            )
            print("INFO: Interface 'Devis' ajoutée.")

        # Gestion des Stocks
        if permission_manager.has_permission(Permission.VIEW_STOCK):
            self.navigationInterface.addSeparator()
            self.addSubInterface(self.stockMovementView, FluentIcon.LIBRARY, "Mouvements de Stock")
            print("INFO: Interface 'Mouvements de Stock' ajoutée.")

        # Administration
        if permission_manager.has_permission(Permission.VIEW_USERS):
            self.navigationInterface.addSeparator()

            # Ajouter l'interface d'administration
            self.addSubInterface(self.userManagementView, FluentIcon.SETTING, "Administration")
            print("INFO: Interface 'Administration' ajoutée.")

            # Ajouter les journaux d'activité comme interface séparée
            if permission_manager.has_permission(Permission.VIEW_USERS):
                self.navigationInterface.addSeparator()
                self.addSubInterface(self.activityLogView, FluentIcon.HISTORY, "Journaux d'Activité")
                print("INFO: Interface 'Journaux d'Activité' ajoutée.")

        # Gestion des Fournisseurs
        # self.navigationInterface.addItem(
        #     routeKey='suppliersView',
        #     icon=FluentIcon.PEOPLE,
        #     text='Fournisseurs',
        #     onClick=lambda: self.stackedWidget.setCurrentWidget(self.supplierView), # Changer pour la bonne vue
        #     position=NavigationItemPosition.TOP,
        #     parent=stock_group
        # )


        # Ventes / Finance
        self.navigationInterface.addSeparator()
        # self.addSubInterface(self.salesView, FluentIcon.DOCUMENT, "Ventes & Factures")
        # print("INFO: Interface 'Ventes & Factures' ajoutée.")
        # Ajouter Dépenses, Tableaux de bord financiers...

        # Inventaire
        self.navigationInterface.addSeparator()
        # self.addSubInterface(self.stockMovementView, FluentIcon.SYNC, "Mouvements Stock")
        # print("INFO: Interface 'Mouvements Stock' ajoutée.")
        # Ajouter Inventaire Physique...

        # Rapports
        self.navigationInterface.addSeparator()
        self.addSubInterface(self.reportGeneratorView, FluentIcon.DOCUMENT, "Rapports")
        print("INFO: Interface 'Rapports' ajoutée.")


        # --- Ajouter des éléments en bas (ex: Paramètres) ---
        self.navigationInterface.addSeparator(NavigationItemPosition.BOTTOM) # A partir du bas
        self.addSubInterface(self.settingsView, FluentIcon.SETTING, "Paramètres", position=NavigationItemPosition.BOTTOM)
        print("INFO: Interface 'Paramètres' ajoutée en bas.")

        print("INFO: Navigation initialisée.")

    def get_widget(self, object_name: str):
        """ Helper pour trouver un widget par son nom d'objet. """
        widget = self.findChild(QWidget, object_name)
        if not widget:
            print(f"AVERTISSEMENT: Widget avec objectName '{object_name}' non trouvé.")
        return widget

    def applyBaseStyle(self):
        """Applique le style de base à la fenêtre principale"""
        self.setStyleSheet("""
            QWidget {
                background-color: #181A20;
                color: #F1F5F9;
                font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
                font-size: 14px;
            }
            QLabel {
                color: #F1F5F9;
                font-size: 15px;

            }
            QFrame, QGroupBox {
                background-color: #23263A;
                border-radius: 12px;
                border: 1px solid #334155;
            }
            QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox {
                background-color: #23263A;
                border: 1.5px solid #334155;
                border-radius: 8px;
                padding: 8px 12px;
                color: #F1F5F9;
                font-size: 15px;
                selection-background-color: #38BDF8;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border: 1.5px solid #38BDF8;
                background-color: #1e293b;
            }
            QCheckBox {
                spacing: 8px;
                font-size: 14px;
                color: #F1F5F9;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 6px;
                border: 2px solid #38BDF8;
                background: #23263A;
            }
            QCheckBox::indicator:checked {
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.8, fx:0.5, fy:0.5, stop:0 #38BDF8, stop:1 #23263A);
                border: 2px solid #38BDF8;
            }
            QPushButton, PrimaryPushButton, TransparentPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #38BDF8, stop:1 #0ea5e9);
                color: #fff;
                border: none;
                border-radius: 10px;
                padding: 10px 24px;
                font-size: 15px;
                font-weight: 600;
                letter-spacing: 1px;
            }
            QPushButton:hover, PrimaryPushButton:hover, TransparentPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0ea5e9, stop:1 #38BDF8);
                color: #fff;
            }
            QTableWidget {
                background-color: #23263A;
                color: #F1F5F9;
                gridline-color: #334155;
                font-size: 15px;
                border-radius: 8px;
            }
            QTableWidget QTableCornerButton::section {
                background: #23263A;
            }
            QHeaderView::section {
                background-color: #23263A;
                color: #38BDF8;
                font-weight: bold;
                border: none;
                border-bottom: 2px solid #38BDF8;
            }
        """)