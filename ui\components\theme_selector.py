"""
Sélecteur de thème moderne pour GSCOM.
Widget permettant de choisir parmi les thèmes disponibles.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QPushButton, QScrollArea
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QPainter, QBrush, QColor

from qfluentwidgets import FluentIcon, IconWidget
from ui.themes.modern_themes import ModernTheme, ThemeColors, modern_theme_manager


class ThemePreview(QFrame):
    """Aperçu d'un thème."""
    
    clicked = pyqtSignal(ModernTheme)
    
    def __init__(self, theme: ModernTheme, parent=None):
        super().__init__(parent)
        self.theme = theme
        self.is_selected = False
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface de l'aperçu."""
        self.setFixedSize(200, 120)
        self.setCursor(Qt.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Nom du thème
        theme_name = self.theme.value.replace("_", " ").title()
        name_label = QLabel(theme_name)
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        
        # Aperçu des couleurs
        colors_frame = QFrame()
        colors_frame.setFixedHeight(60)
        colors_layout = QHBoxLayout(colors_frame)
        colors_layout.setContentsMargins(5, 5, 5, 5)
        colors_layout.setSpacing(2)
        
        # Obtenir les couleurs du thème
        colors = ThemeColors.get_colors(self.theme)
        
        # Créer des échantillons de couleur
        color_samples = [
            colors['primary'],
            colors['secondary'],
            colors['accent'],
            colors['background'],
            colors['surface']
        ]
        
        for color in color_samples:
            sample = QFrame()
            sample.setFixedSize(30, 50)
            sample.setStyleSheet(f"""
                QFrame {{
                    background-color: {color};
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 4px;
                }}
            """)
            colors_layout.addWidget(sample)
            
        layout.addWidget(name_label)
        layout.addWidget(colors_frame)
        
        # Appliquer le style de base
        self.update_style()
        
    def update_style(self):
        """Met à jour le style de l'aperçu."""
        colors = ThemeColors.get_colors(self.theme)
        
        if self.is_selected:
            border_color = colors['primary']
            border_width = "3px"
            background = f"rgba({self._hex_to_rgb(colors['primary'])}, 0.1)"
        else:
            border_color = colors['border']
            border_width = "1px"
            background = colors['surface']
            
        self.setStyleSheet(f"""
            ThemePreview {{
                background-color: {background};
                border: {border_width} solid {border_color};
                border-radius: 8px;
            }}
            ThemePreview:hover {{
                border: 2px solid {colors['primary']};
                background-color: rgba({self._hex_to_rgb(colors['primary'])}, 0.05);
            }}
            QLabel {{
                color: {colors['text']};
                background: transparent;
            }}
        """)
        
    def _hex_to_rgb(self, hex_color):
        """Convertit une couleur hex en RGB."""
        hex_color = hex_color.lstrip('#')
        return ', '.join(str(int(hex_color[i:i+2], 16)) for i in (0, 2, 4))
        
    def set_selected(self, selected):
        """Définit l'état de sélection."""
        self.is_selected = selected
        self.update_style()
        
    def mousePressEvent(self, event):
        """Gère le clic sur l'aperçu."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.theme)
        super().mousePressEvent(event)


class ThemeSelector(QWidget):
    """Sélecteur de thème avec aperçus."""
    
    themeSelected = pyqtSignal(ModernTheme)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_theme = modern_theme_manager.get_current_theme()
        self.theme_previews = {}
        self.setupUI()
        
    def setupUI(self):
        """Configure l'interface du sélecteur."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # Titre
        title_label = QLabel("Choisir un thème")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Zone de défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Widget de contenu
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        
        # Thèmes sombres
        dark_section = self.create_theme_section(
            "Thèmes sombres",
            [
                ModernTheme.DARK_BLUE,
                ModernTheme.DARK_PURPLE,
                ModernTheme.DARK_GREEN,
                ModernTheme.CYBERPUNK,
                ModernTheme.OCEAN,
                ModernTheme.SUNSET,
                ModernTheme.FOREST
            ]
        )
        content_layout.addWidget(dark_section)
        
        # Thèmes clairs
        light_section = self.create_theme_section(
            "Thèmes clairs",
            [
                ModernTheme.LIGHT_BLUE,
                ModernTheme.LIGHT_PURPLE,
                ModernTheme.LIGHT_GREEN
            ]
        )
        content_layout.addWidget(light_section)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        apply_button = QPushButton("Appliquer")
        apply_button.clicked.connect(self.apply_theme)
        apply_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3B82F6, stop:1 #1D4ED8);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1D4ED8, stop:1 #3B82F6);
            }
        """)
        
        buttons_layout.addWidget(apply_button)
        layout.addLayout(buttons_layout)
        
    def create_theme_section(self, title, themes):
        """Crée une section de thèmes."""
        section = QFrame()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(10)
        
        # Titre de la section
        section_title = QLabel(title)
        section_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        section_layout.addWidget(section_title)
        
        # Grille des thèmes
        themes_grid = QGridLayout()
        themes_grid.setSpacing(10)
        
        for i, theme in enumerate(themes):
            row = i // 3
            col = i % 3
            
            preview = ThemePreview(theme)
            preview.clicked.connect(self.on_theme_clicked)
            
            # Marquer le thème actuel comme sélectionné
            if theme == self.current_theme:
                preview.set_selected(True)
                
            self.theme_previews[theme] = preview
            themes_grid.addWidget(preview, row, col)
            
        section_layout.addLayout(themes_grid)
        
        return section
        
    def on_theme_clicked(self, theme):
        """Appelé quand un thème est cliqué."""
        # Désélectionner tous les thèmes
        for preview in self.theme_previews.values():
            preview.set_selected(False)
            
        # Sélectionner le nouveau thème
        self.theme_previews[theme].set_selected(True)
        self.current_theme = theme
        
    def apply_theme(self):
        """Applique le thème sélectionné."""
        modern_theme_manager.set_theme(self.current_theme)
        self.themeSelected.emit(self.current_theme)
        
    def set_current_theme(self, theme):
        """Définit le thème actuel."""
        # Désélectionner l'ancien thème
        if self.current_theme in self.theme_previews:
            self.theme_previews[self.current_theme].set_selected(False)
            
        # Sélectionner le nouveau thème
        self.current_theme = theme
        if theme in self.theme_previews:
            self.theme_previews[theme].set_selected(True)
