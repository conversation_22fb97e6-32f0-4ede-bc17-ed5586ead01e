"""
Package models pour la base de données GSCOM.
Contient tous les modèles SQLAlchemy.
"""

# Import des modèles
from .customer import Customer
from .product import Product
from .sales import Sale, SaleItem, SaleStatus
from .inventory import StockMovement, MovementType
from .finance import FinancialTransaction, TransactionType

__all__ = [
    'Customer',
    'Product', 
    'Sale',
    'SaleItem',
    'SaleStatus',
    'StockMovement',
    'MovementType',
    'FinancialTransaction',
    'TransactionType'
]

print("INFO: Package 'database.models' chargé.")
