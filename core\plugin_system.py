"""
Système de plugins pour GSCOM.
Architecture extensible pour ajouter des fonctionnalités via des plugins.
"""
import os
import sys
import importlib
import inspect
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QWidget
import json


class PluginInterface(ABC):
    """Interface de base pour tous les plugins."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Nom du plugin."""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Version du plugin."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Description du plugin."""
        pass
    
    @property
    @abstractmethod
    def author(self) -> str:
        """Auteur du plugin."""
        pass
    
    @abstractmethod
    def initialize(self, app_context: Dict[str, Any]) -> bool:
        """Initialise le plugin."""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """<PERSON><PERSON>ie les ressources du plugin."""
        pass
    
    def get_menu_items(self) -> List[Dict[str, Any]]:
        """Retourne les éléments de menu du plugin."""
        return []
    
    def get_toolbar_items(self) -> List[Dict[str, Any]]:
        """Retourne les éléments de barre d'outils du plugin."""
        return []
    
    def get_widgets(self) -> List[QWidget]:
        """Retourne les widgets du plugin."""
        return []


class UIPluginInterface(PluginInterface):
    """Interface pour les plugins d'interface utilisateur."""
    
    @abstractmethod
    def create_widget(self, parent=None) -> QWidget:
        """Crée le widget principal du plugin."""
        pass
    
    def get_icon(self) -> str:
        """Retourne le chemin de l'icône du plugin."""
        return ""
    
    def get_category(self) -> str:
        """Retourne la catégorie du plugin."""
        return "General"


class DataPluginInterface(PluginInterface):
    """Interface pour les plugins de données."""
    
    @abstractmethod
    def import_data(self, source: str, options: Dict[str, Any]) -> bool:
        """Importe des données depuis une source."""
        pass
    
    @abstractmethod
    def export_data(self, destination: str, data: Any, options: Dict[str, Any]) -> bool:
        """Exporte des données vers une destination."""
        pass
    
    def get_supported_formats(self) -> List[str]:
        """Retourne les formats supportés."""
        return []


class ReportPluginInterface(PluginInterface):
    """Interface pour les plugins de rapports."""
    
    @abstractmethod
    def generate_report(self, report_type: str, parameters: Dict[str, Any]) -> bytes:
        """Génère un rapport."""
        pass
    
    def get_report_types(self) -> List[Dict[str, str]]:
        """Retourne les types de rapports disponibles."""
        return []


class PluginMetadata:
    """Métadonnées d'un plugin."""
    
    def __init__(self, plugin_path: str):
        self.plugin_path = plugin_path
        self.manifest_path = os.path.join(plugin_path, "manifest.json")
        self.metadata = self.load_metadata()
        
    def load_metadata(self) -> Dict[str, Any]:
        """Charge les métadonnées depuis le manifest."""
        if os.path.exists(self.manifest_path):
            try:
                with open(self.manifest_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Erreur lors du chargement du manifest {self.manifest_path}: {e}")
                
        return {}
    
    @property
    def name(self) -> str:
        return self.metadata.get("name", "Plugin Inconnu")
    
    @property
    def version(self) -> str:
        return self.metadata.get("version", "1.0.0")
    
    @property
    def description(self) -> str:
        return self.metadata.get("description", "")
    
    @property
    def author(self) -> str:
        return self.metadata.get("author", "Inconnu")
    
    @property
    def main_module(self) -> str:
        return self.metadata.get("main", "main.py")
    
    @property
    def dependencies(self) -> List[str]:
        return self.metadata.get("dependencies", [])
    
    @property
    def min_app_version(self) -> str:
        return self.metadata.get("min_app_version", "1.0.0")
    
    @property
    def enabled(self) -> bool:
        return self.metadata.get("enabled", True)


class PluginManager(QObject):
    """Gestionnaire de plugins."""
    
    pluginLoaded = pyqtSignal(str)  # plugin_name
    pluginUnloaded = pyqtSignal(str)  # plugin_name
    pluginError = pyqtSignal(str, str)  # plugin_name, error_message
    
    def __init__(self, plugins_directory: str = "plugins"):
        super().__init__()
        self.plugins_directory = plugins_directory
        self.loaded_plugins: Dict[str, PluginInterface] = {}
        self.plugin_metadata: Dict[str, PluginMetadata] = {}
        self.app_context = {}
        
        # Créer le dossier plugins s'il n'existe pas
        os.makedirs(self.plugins_directory, exist_ok=True)
        
    def set_app_context(self, context: Dict[str, Any]):
        """Définit le contexte de l'application."""
        self.app_context = context
        
    def discover_plugins(self) -> List[str]:
        """Découvre tous les plugins disponibles."""
        plugins = []
        
        if not os.path.exists(self.plugins_directory):
            return plugins
            
        for item in os.listdir(self.plugins_directory):
            plugin_path = os.path.join(self.plugins_directory, item)
            
            if os.path.isdir(plugin_path):
                manifest_path = os.path.join(plugin_path, "manifest.json")
                if os.path.exists(manifest_path):
                    plugins.append(item)
                    self.plugin_metadata[item] = PluginMetadata(plugin_path)
                    
        return plugins
        
    def load_plugin(self, plugin_name: str) -> bool:
        """Charge un plugin."""
        if plugin_name in self.loaded_plugins:
            return True
            
        plugin_path = os.path.join(self.plugins_directory, plugin_name)
        
        if not os.path.exists(plugin_path):
            self.pluginError.emit(plugin_name, "Dossier du plugin introuvable")
            return False
            
        metadata = self.plugin_metadata.get(plugin_name)
        if not metadata:
            self.pluginError.emit(plugin_name, "Métadonnées du plugin introuvables")
            return False
            
        if not metadata.enabled:
            return False
            
        try:
            # Ajouter le chemin du plugin au sys.path
            if plugin_path not in sys.path:
                sys.path.insert(0, plugin_path)
                
            # Importer le module principal
            main_module_name = os.path.splitext(metadata.main_module)[0]
            module = importlib.import_module(main_module_name)
            
            # Trouver la classe du plugin
            plugin_class = None
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, PluginInterface) and 
                    obj != PluginInterface):
                    plugin_class = obj
                    break
                    
            if not plugin_class:
                self.pluginError.emit(plugin_name, "Classe de plugin introuvable")
                return False
                
            # Instancier le plugin
            plugin_instance = plugin_class()
            
            # Initialiser le plugin
            if plugin_instance.initialize(self.app_context):
                self.loaded_plugins[plugin_name] = plugin_instance
                self.pluginLoaded.emit(plugin_name)
                return True
            else:
                self.pluginError.emit(plugin_name, "Échec de l'initialisation")
                return False
                
        except Exception as e:
            self.pluginError.emit(plugin_name, f"Erreur de chargement: {str(e)}")
            return False
            
    def unload_plugin(self, plugin_name: str) -> bool:
        """Décharge un plugin."""
        if plugin_name not in self.loaded_plugins:
            return True
            
        try:
            plugin = self.loaded_plugins[plugin_name]
            plugin.cleanup()
            del self.loaded_plugins[plugin_name]
            self.pluginUnloaded.emit(plugin_name)
            return True
            
        except Exception as e:
            self.pluginError.emit(plugin_name, f"Erreur de déchargement: {str(e)}")
            return False
            
    def reload_plugin(self, plugin_name: str) -> bool:
        """Recharge un plugin."""
        self.unload_plugin(plugin_name)
        return self.load_plugin(plugin_name)
        
    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """Récupère une instance de plugin."""
        return self.loaded_plugins.get(plugin_name)
        
    def get_plugins_by_type(self, plugin_type: type) -> List[PluginInterface]:
        """Récupère tous les plugins d'un type donné."""
        return [plugin for plugin in self.loaded_plugins.values() 
                if isinstance(plugin, plugin_type)]
                
    def get_all_plugins(self) -> Dict[str, PluginInterface]:
        """Récupère tous les plugins chargés."""
        return self.loaded_plugins.copy()
        
    def enable_plugin(self, plugin_name: str) -> bool:
        """Active un plugin."""
        metadata = self.plugin_metadata.get(plugin_name)
        if metadata:
            metadata.metadata["enabled"] = True
            self.save_plugin_metadata(plugin_name, metadata)
            return True
        return False
        
    def disable_plugin(self, plugin_name: str) -> bool:
        """Désactive un plugin."""
        self.unload_plugin(plugin_name)
        metadata = self.plugin_metadata.get(plugin_name)
        if metadata:
            metadata.metadata["enabled"] = False
            self.save_plugin_metadata(plugin_name, metadata)
            return True
        return False
        
    def save_plugin_metadata(self, plugin_name: str, metadata: PluginMetadata):
        """Sauvegarde les métadonnées d'un plugin."""
        try:
            with open(metadata.manifest_path, 'w', encoding='utf-8') as f:
                json.dump(metadata.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des métadonnées: {e}")
            
    def install_plugin(self, plugin_archive_path: str) -> bool:
        """Installe un plugin depuis une archive."""
        # TODO: Implémenter l'installation de plugins
        return False
        
    def uninstall_plugin(self, plugin_name: str) -> bool:
        """Désinstalle un plugin."""
        # TODO: Implémenter la désinstallation de plugins
        return False
        
    def check_dependencies(self, plugin_name: str) -> List[str]:
        """Vérifie les dépendances d'un plugin."""
        metadata = self.plugin_metadata.get(plugin_name)
        if not metadata:
            return ["Métadonnées introuvables"]
            
        missing_deps = []
        for dep in metadata.dependencies:
            try:
                importlib.import_module(dep)
            except ImportError:
                missing_deps.append(dep)
                
        return missing_deps
        
    def get_plugin_info(self, plugin_name: str) -> Dict[str, Any]:
        """Récupère les informations d'un plugin."""
        metadata = self.plugin_metadata.get(plugin_name)
        if not metadata:
            return {}
            
        plugin = self.loaded_plugins.get(plugin_name)
        
        return {
            "name": metadata.name,
            "version": metadata.version,
            "description": metadata.description,
            "author": metadata.author,
            "enabled": metadata.enabled,
            "loaded": plugin is not None,
            "dependencies": metadata.dependencies,
            "min_app_version": metadata.min_app_version
        }


# Instance globale du gestionnaire de plugins
plugin_manager = PluginManager()


def create_sample_plugin():
    """Crée un plugin d'exemple."""
    plugin_dir = os.path.join("plugins", "sample_plugin")
    os.makedirs(plugin_dir, exist_ok=True)
    
    # Manifest
    manifest = {
        "name": "Plugin d'Exemple",
        "version": "1.0.0",
        "description": "Un plugin d'exemple pour démontrer le système",
        "author": "GSCOM Team",
        "main": "main.py",
        "dependencies": [],
        "min_app_version": "2.0.0",
        "enabled": True
    }
    
    manifest_path = os.path.join(plugin_dir, "manifest.json")
    with open(manifest_path, 'w', encoding='utf-8') as f:
        json.dump(manifest, f, indent=2, ensure_ascii=False)
        
    # Code du plugin
    plugin_code = '''"""
Plugin d'exemple pour GSCOM.
"""
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton
from core.plugin_system import UIPluginInterface


class SamplePlugin(UIPluginInterface):
    """Plugin d'exemple."""
    
    @property
    def name(self) -> str:
        return "Plugin d'Exemple"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Un plugin d'exemple pour démontrer le système"
    
    @property
    def author(self) -> str:
        return "GSCOM Team"
    
    def initialize(self, app_context):
        """Initialise le plugin."""
        print(f"Plugin {self.name} initialisé")
        return True
    
    def cleanup(self):
        """Nettoie les ressources."""
        print(f"Plugin {self.name} nettoyé")
    
    def create_widget(self, parent=None):
        """Crée le widget du plugin."""
        widget = QWidget(parent)
        layout = QVBoxLayout(widget)
        
        label = QLabel("Ceci est un plugin d'exemple")
        button = QPushButton("Bouton du plugin")
        
        layout.addWidget(label)
        layout.addWidget(button)
        
        return widget
    
    def get_category(self):
        return "Exemple"
'''
    
    main_path = os.path.join(plugin_dir, "main.py")
    with open(main_path, 'w', encoding='utf-8') as f:
        f.write(plugin_code)
        
    print(f"Plugin d'exemple créé dans {plugin_dir}")


if __name__ == "__main__":
    # Créer un plugin d'exemple
    create_sample_plugin()
    
    # Tester le gestionnaire de plugins
    manager = PluginManager()
    plugins = manager.discover_plugins()
    print(f"Plugins découverts: {plugins}")
    
    for plugin_name in plugins:
        info = manager.get_plugin_info(plugin_name)
        print(f"Plugin: {info}")
        
        if manager.load_plugin(plugin_name):
            print(f"Plugin {plugin_name} chargé avec succès")
        else:
            print(f"Échec du chargement du plugin {plugin_name}")
